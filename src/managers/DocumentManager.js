import IDocumentManager from '../interfaces/IDocumentManager.js';
import { getYDoc, docs, getDocumentStateSize, applyUpdateToDoc } from '../utils/y-websocket-utils.js';

class DocumentManager extends IDocumentManager {
  constructor(logger, config = {}) {
    super();
    this.logger = logger;
    this.config = config;
    this.documentStats = new Map();
    this.gcEnabled = config.gcEnabled !== false;

    if (config.cleanupInterval) {
      this.cleanupInterval = setInterval(() => {
        this.cleanup();
      }, config.cleanupInterval);
    }
  }

  getDocument(documentId) {
    try {
      const doc = getYDoc(documentId, this.gcEnabled);

      if (!this.documentStats.has(documentId)) {
        this.documentStats.set(documentId, {
          createdAt: new Date(),
          lastAccessed: new Date(),
          updateCount: 0,
          connectionCount: 0
        });
      }

      const stats = this.documentStats.get(documentId);
      if (stats) {
        stats.lastAccessed = new Date();
      }

      return doc;
    } catch (error) {
      this.logger.error('Failed to get document', error, { documentId });
      throw error;
    }
  }

  hasDocument(documentId) {
    return docs.has(documentId);
  }

  removeDocument(documentId) {
    try {
      const doc = docs.get(documentId);
      if (!doc) {
        return false;
      }

      if (doc.conns.size > 0) {
        throw new Error('Cannot remove document with active connections');
      }

      doc.destroy();
      docs.delete(documentId);
      this.documentStats.delete(documentId);
      return true;
    } catch (error) {
      this.logger.error('Failed to remove document', error, { documentId });
      throw error;
    }
  }

  getDocumentStats(documentId) {
    try {
      const doc = docs.get(documentId);
      const stats = this.documentStats.get(documentId);

      if (!doc || !stats) {
        return null;
      }

      return {
        ...stats,
        size: getDocumentStateSize(doc),
        connectionCount: doc.conns.size,
        exists: true
      };
    } catch (error) {
      this.logger.error('Failed to get document stats', error, { documentId });
      throw error;
    }
  }

  getAllDocumentIds() {
    return Array.from(docs.keys());
  }

  cleanup() {
    try {
      const now = new Date();
      const maxIdleTime = this.config.maxIdleTime || 30 * 60 * 1000;
      let cleanedCount = 0;

      this.documentStats.forEach((stats, documentId) => {
        const idleTime = now - stats.lastAccessed;
        if (idleTime > maxIdleTime && stats.connectionCount === 0) {
          this.removeDocument(documentId);
          cleanedCount++;
        }
      });

      return cleanedCount;
    } catch (error) {
      this.logger.error('Document cleanup failed', error);
      throw error;
    }
  }

  applyUpdate(documentId, update, origin = null) {
    try {
      const doc = this.getDocument(documentId);
      applyUpdateToDoc(doc, update, origin);

      const stats = this.documentStats.get(documentId);
      if (stats) {
        stats.updateCount++;
        stats.lastAccessed = new Date();
      }

      return true;
    } catch (error) {
      this.logger.error('Failed to apply update to document', error, {
        documentId,
        updateSize: update ? update.length : 0
      });
      throw error;
    }
  }

  setupDocumentListeners(doc, documentId) {
    doc.on('update', (update) => {
      const stats = this.documentStats.get(documentId);
      if (stats) {
        stats.updateCount++;
        stats.lastAccessed = new Date();
      }
    });
  }

  updateConnectionCount(documentId, count) {
    const stats = this.documentStats.get(documentId);
    if (stats) {
      stats.connectionCount = count;
    }
  }

  getOverallStats() {
    try {
      return {
        totalDocuments: docs.size,
        totalStats: this.documentStats.size,
        memoryUsage: process.memoryUsage()
      };
    } catch (error) {
      this.logger.error('Failed to get overall stats', error);
      throw error;
    }
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    docs.forEach((doc) => {
      doc.destroy();
    });

    docs.clear();
    this.documentStats.clear();
  }
}

export default DocumentManager;
