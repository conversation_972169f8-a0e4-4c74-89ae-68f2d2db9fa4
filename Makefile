# 🚀 Real-time Y.js WebSocket Server - Makefile
# Easy commands for development, testing, and deployment

# Variables - can be overridden by environment variables
DOCKER_IMAGE_NAME ?= realtime-yjs-server
DOCKER_TAG ?= latest
CONTAINER_NAME ?= yjs-server
PORT ?= 1234
FRONTEND_PORT ?= 3000

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

.PHONY: help install dev build start stop restart logs clean test docker-build docker-run docker-stop docker-clean docker-status docker-test production scale-up scale-down generate-haproxy-config auth-logs auth-status connection-test

# Default target
help: ## Show this help message
	@echo "$(BLUE)🚀 Real-time Y.js WebSocket Server$(NC)"
	@echo "$(YELLOW)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development Commands
install: ## Install dependencies
	@echo "$(YELLOW)📦 Installing dependencies...$(NC)"
	npm install
	@echo "$(GREEN)✅ Dependencies installed$(NC)"

dev: ## Start development server with auto-reload
	@echo "$(YELLOW)🔧 Starting development server...$(NC)"
	npm run dev

start: ## Start production server
	@echo "$(YELLOW)🚀 Starting production server...$(NC)"
	npm start

stop: ## Stop the server (if running with PM2)
	@echo "$(YELLOW)🛑 Stopping server...$(NC)"
	-pkill -f "node src/index.js"
	@echo "$(GREEN)✅ Server stopped$(NC)"

restart: stop start ## Restart the server

logs: ## Show server logs
	@echo "$(YELLOW)📋 Showing logs...$(NC)"
	tail -f logs/app.log 2>/dev/null || echo "$(RED)❌ No log file found$(NC)"

test: ## Run tests
	@echo "$(YELLOW)🧪 Running tests...$(NC)"
	npm test

clean: ## Clean node_modules and logs
	@echo "$(YELLOW)🧹 Cleaning up...$(NC)"
	rm -rf node_modules
	rm -rf logs/*.log
	rm -rf package-lock.json
	@echo "$(GREEN)✅ Cleanup complete$(NC)"

# Docker Commands (Scalable Setup)
docker-build: ## Build Docker images for scalable setup
	@echo "$(YELLOW)🐳 Building Docker images for scalable setup...$(NC)"
	@docker-compose -f docker-compose.scalable.yml build
	@echo "$(GREEN)✅ Docker images built for scalable setup$(NC)"



docker-run: docker-build ## Build and run scalable setup
	@echo "$(YELLOW)🚀 Starting scalable Docker setup...$(NC)"
	@docker-compose -f docker-compose.scalable.yml up -d
	@echo "$(GREEN)✅ Scalable setup started!$(NC)"
	@echo "$(CYAN)📊 Access points:$(NC)"
	@echo "  • Main app: http://localhost:$(PORT)"
	@echo "  • Load balancer stats: http://localhost:8080/stats"
	@echo "  • Redis: localhost:6379"



docker-stop: ## Stop scalable Docker setup
	@echo "$(YELLOW)🛑 Stopping scalable Docker setup...$(NC)"
	@docker-compose -f docker-compose.scalable.yml down
	@echo "$(GREEN)✅ Scalable setup stopped$(NC)"

docker-logs: ## Show logs from scalable setup
	@echo "$(YELLOW)📋 Scalable setup logs:$(NC)"
	@echo "$(BLUE)💡 Press Ctrl+C to stop following logs$(NC)"
	@echo "$(CYAN)--- Following logs from all services ---$(NC)"
	@docker-compose -f docker-compose.scalable.yml logs --tail=50 -f

docker-shell: ## Open shell in running Y.js server container
	@echo "$(YELLOW)🐚 Opening shell in Y.js server container...$(NC)"
	@docker exec -it yjs-server-1 /bin/sh

auth-logs: ## Show authentication-related logs from all servers
	@echo "$(YELLOW)🔐 Authentication logs:$(NC)"
	@echo "$(BLUE)💡 Press Ctrl+C to stop following logs$(NC)"
	@echo "$(CYAN)--- Filtering for authentication events ---$(NC)"
	@docker-compose -f docker-compose.scalable.yml logs -f | grep -E "(auth|Auth|JWT|token|Token|permission|Permission|unauthorized|Unauthorized)" --color=always

auth-status: ## Check authentication status across all services
	@echo "$(YELLOW)🔐 Authentication Status Check:$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Environment Configuration:$(NC)"
	@if [ -f .env ]; then \
		AUTH_ENABLED=$$(grep "^AUTH_ENABLED=" .env | cut -d'=' -f2); \
		echo "  AUTH_ENABLED in .env: $$AUTH_ENABLED"; \
	else \
		echo "  $(RED)❌ .env file not found$(NC)"; \
	fi
	@echo ""
	@echo "$(BLUE)📋 Server Status:$(NC)"
	@echo "  Testing yjs-server-1:"
	@HEALTH=$$(docker exec yjs-server-1 curl -s http://localhost:1234/health 2>/dev/null); \
	if [ $$? -eq 0 ]; then \
		AUTH_STATUS=$$(echo "$$HEALTH" | jq -r '.auth.authEnabled' 2>/dev/null); \
		TIMESTAMP=$$(echo "$$HEALTH" | jq -r '.auth.timestamp' 2>/dev/null); \
		echo "    Status: $(GREEN)✅ Running$(NC)"; \
		echo "    Auth Enabled: $$AUTH_STATUS"; \
		echo "    Last Check: $$TIMESTAMP"; \
	else \
		echo "    Status: $(RED)❌ Not responding$(NC)"; \
	fi
	@echo ""
	@echo "  Testing yjs-server-2:"
	@HEALTH=$$(docker exec yjs-server-2 curl -s http://localhost:1234/health 2>/dev/null); \
	if [ $$? -eq 0 ]; then \
		AUTH_STATUS=$$(echo "$$HEALTH" | jq -r '.auth.authEnabled' 2>/dev/null); \
		TIMESTAMP=$$(echo "$$HEALTH" | jq -r '.auth.timestamp' 2>/dev/null); \
		echo "    Status: $(GREEN)✅ Running$(NC)"; \
		echo "    Auth Enabled: $$AUTH_STATUS"; \
		echo "    Last Check: $$TIMESTAMP"; \
	else \
		echo "    Status: $(RED)❌ Not responding$(NC)"; \
	fi
	@echo ""
	@echo "$(BLUE)📋 Load Balancer Status:$(NC)"
	@HEALTH=$$(curl -s http://localhost:1234/health 2>/dev/null); \
	if [ $$? -eq 0 ]; then \
		AUTH_STATUS=$$(echo "$$HEALTH" | jq -r '.auth.authEnabled' 2>/dev/null); \
		echo "  Load Balancer: $(GREEN)✅ Running$(NC)"; \
		echo "  Auth Enabled: $$AUTH_STATUS"; \
		if [ "$$AUTH_STATUS" = "true" ]; then \
			echo "  $(YELLOW)💡 Generate test token: npm run generate-demo-token$(NC)"; \
		fi; \
	else \
		echo "  Load Balancer: $(RED)❌ Not responding$(NC)"; \
	fi

connection-test: ## Test WebSocket connections with and without authentication
	@echo "$(YELLOW)🔌 WebSocket Connection Test:$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Testing unauthenticated connection...$(NC)"
	@timeout 5 wscat -c ws://localhost:1234/test-document 2>&1 | head -3 || echo "  $(RED)❌ Unauthenticated connection failed (expected if auth enabled)$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Generating test token...$(NC)"
	@TOKEN=$$(npm run generate-demo-token --silent | grep "Token:" | cut -d' ' -f2); \
	echo "  Token generated: $${TOKEN:0:50}..."; \
	echo ""; \
	echo "$(BLUE)📋 Testing authenticated connection...$(NC)"; \
	timeout 5 wscat -c "ws://localhost:1234/demo-document?token=$$TOKEN" 2>&1 | head -3 || echo "  $(RED)❌ Authenticated connection failed$(NC)"

docker-clean: docker-stop ## Clean Docker images and containers
	@echo "$(YELLOW)🧹 Cleaning Docker resources...$(NC)"
	-docker rmi $(DOCKER_IMAGE_NAME):$(DOCKER_TAG)
	@docker system prune -f
	@echo "$(GREEN)✅ Docker cleanup complete$(NC)"

# 🚀 Additional Scalable Commands
docker-status: ## Show status of scalable setup
	@echo "$(YELLOW)📊 Scalable setup status:$(NC)"
	@docker-compose -f docker-compose.scalable.yml ps
	@echo ""
	@echo "$(CYAN)🔍 Testing load balancer:$(NC)"
	@curl -s http://localhost:1234/health | jq . || echo "$(RED)❌ Load balancer not responding$(NC)"

docker-test: ## Test load balancing with multiple requests
	@echo "$(YELLOW)🧪 Testing load balancing...$(NC)"
	@for i in {1..5}; do \
		echo "Request $$i:"; \
		curl -s http://localhost:1234/health | jq -r '.timestamp' || echo "Failed"; \
		sleep 1; \
	done
	@echo "$(GREEN)✅ Load balancing test complete$(NC)"

compose-logs: ## Show docker-compose logs
	@echo "$(YELLOW)📋 Showing docker-compose logs...$(NC)"
	docker-compose logs -f

compose-build: ## Build docker-compose services
	@echo "$(YELLOW)🔨 Building docker-compose services...$(NC)"
	docker-compose build
	@echo "$(GREEN)✅ Services built$(NC)"

# Production Commands
production: ## Deploy to production with nginx
	@echo "$(YELLOW)🚀 Deploying to production...$(NC)"
	docker-compose --profile production up -d
	@echo "$(GREEN)✅ Production deployment complete$(NC)"

# Health Check Commands
health: ## Check server health
	@echo "$(YELLOW)🏥 Checking server health...$(NC)"
	@curl -s http://localhost:$(PORT)/health | jq . || echo "$(RED)❌ Health check failed$(NC)"

stats: ## Show server statistics
	@echo "$(YELLOW)📊 Server statistics:$(NC)"
	@curl -s http://localhost:$(PORT)/api/stats | jq . || echo "$(RED)❌ Stats unavailable$(NC)"

# Frontend Commands
frontend-install: ## Install frontend dependencies
	@echo "$(YELLOW)📦 Installing frontend dependencies...$(NC)"
	cd tiptap-yjs-react && npm install
	@echo "$(GREEN)✅ Frontend dependencies installed$(NC)"

frontend-start: ## Start frontend development server
	@echo "$(YELLOW)🎨 Starting frontend server...$(NC)"
	cd tiptap-yjs-react && npm start

frontend-build: ## Build frontend for production
	@echo "$(YELLOW)🔨 Building frontend...$(NC)"
	cd tiptap-yjs-react && npm run build
	@echo "$(GREEN)✅ Frontend built$(NC)"

# Full Stack Commands
full-install: install frontend-install ## Install all dependencies (backend + frontend)

full-dev: ## Start both backend and frontend in development
	@echo "$(YELLOW)🚀 Starting full development environment...$(NC)"
	@echo "$(BLUE)Starting backend server...$(NC)"
	npm run dev &
	@echo "$(BLUE)Waiting for backend to start...$(NC)"
	sleep 3
	@echo "$(BLUE)Starting frontend server...$(NC)"
	cd tiptap-yjs-react && npm start

full-stop: ## Stop all development servers
	@echo "$(YELLOW)🛑 Stopping all servers...$(NC)"
	-pkill -f "node src/index.js"
	-pkill -f "react-scripts start"
	@echo "$(GREEN)✅ All servers stopped$(NC)"

# Utility Commands
check-ports: ## Check if ports are available
	@echo "$(YELLOW)🔍 Checking ports...$(NC)"
	@echo "Backend port $(PORT):"
	@lsof -i :$(PORT) || echo "  $(GREEN)✅ Port $(PORT) is available$(NC)"
	@echo "Frontend port $(FRONTEND_PORT):"
	@lsof -i :$(FRONTEND_PORT) || echo "  $(GREEN)✅ Port $(FRONTEND_PORT) is available$(NC)"

setup: install frontend-install docker-build ## Complete project setup
	@echo "$(GREEN)🎉 Project setup complete!$(NC)"
	@echo "$(YELLOW)Next steps:$(NC)"
	@echo "  $(BLUE)make dev$(NC)          - Start development server"
	@echo "  $(BLUE)make frontend-start$(NC) - Start frontend (in another terminal)"
	@echo "  $(BLUE)make docker-run$(NC)     - Run with Docker"
	@echo "  $(BLUE)make full-dev$(NC)       - Start both backend and frontend"

# Quick start commands
quick-start: setup full-dev ## Complete setup and start development environment

# Monitoring Commands
monitor: ## Monitor server in real-time
	@echo "$(YELLOW)📊 Monitoring server...$(NC)"
	@while true; do \
		clear; \
		echo "$(BLUE)=== Server Health ===$(NC)"; \
		curl -s http://localhost:$(PORT)/health 2>/dev/null | jq . || echo "$(RED)Server not responding$(NC)"; \
		echo ""; \
		echo "$(BLUE)=== Server Stats ===$(NC)"; \
		curl -s http://localhost:$(PORT)/api/stats 2>/dev/null | jq . || echo "$(RED)Stats not available$(NC)"; \
		echo ""; \
		echo "$(YELLOW)Press Ctrl+C to stop monitoring$(NC)"; \
		sleep 5; \
	done

# 🚀 Scalable Production Commands
generate-haproxy-config: ## Generate HAProxy configuration from environment variables
	@echo "$(YELLOW)🔧 Generating HAProxy configuration...$(NC)"
	./scripts/generate-haproxy-config.sh

scale-up: generate-haproxy-config ## Scale up the application (2 Y.js servers + HAProxy + Redis)
	@echo "$(YELLOW)🚀 Scaling up application...$(NC)"
	@echo "$(BLUE)📋 Step 1: Starting Docker containers...$(NC)"
	docker-compose -f docker-compose.scalable.yml up -d
	@echo "$(BLUE)📋 Step 2: Waiting for services to initialize...$(NC)"
	@sleep 5
	@echo "$(BLUE)📋 Step 3: Checking container status...$(NC)"
	@docker-compose -f docker-compose.scalable.yml ps
	@echo ""
	@echo "$(BLUE)📋 Step 4: Testing Redis connection...$(NC)"
	@docker exec redis redis-cli ping 2>/dev/null && echo "$(GREEN)✅ Redis is responding$(NC)" || echo "$(RED)❌ Redis connection failed$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Step 5: Testing Y.js server instances...$(NC)"
	@echo "  Testing yjs-server-1:"
	@curl -s http://localhost:1234/health 2>/dev/null | jq -r '.auth.authEnabled' >/dev/null && echo "    $(GREEN)✅ yjs-server-1 is healthy$(NC)" || echo "    $(RED)❌ yjs-server-1 not responding$(NC)"
	@echo "  Testing yjs-server-2:"
	@docker exec yjs-server-2 curl -s http://localhost:1234/health 2>/dev/null >/dev/null && echo "    $(GREEN)✅ yjs-server-2 is healthy$(NC)" || echo "    $(RED)❌ yjs-server-2 not responding$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Step 6: Testing HAProxy load balancer...$(NC)"
	@curl -s http://localhost:1234/health 2>/dev/null | jq . >/dev/null && echo "    $(GREEN)✅ HAProxy load balancer is working$(NC)" || echo "    $(RED)❌ HAProxy load balancer failed$(NC)"
	@echo ""
	@echo "$(BLUE)📋 Step 7: Checking authentication status...$(NC)"
	@AUTH_STATUS=$$(curl -s http://localhost:1234/health 2>/dev/null | jq -r '.auth.authEnabled' 2>/dev/null); \
	if [ "$$AUTH_STATUS" = "true" ]; then \
		echo "    $(GREEN)✅ JWT Authentication: ENABLED$(NC)"; \
		echo "    $(YELLOW)💡 Use 'npm run generate-demo-token' to create test tokens$(NC)"; \
	elif [ "$$AUTH_STATUS" = "false" ]; then \
		echo "    $(YELLOW)⚠️  JWT Authentication: DISABLED$(NC)"; \
		echo "    $(BLUE)💡 Set AUTH_ENABLED=true in .env to enable authentication$(NC)"; \
	else \
		echo "    $(RED)❌ Could not determine authentication status$(NC)"; \
	fi
	@echo ""
	@echo "$(BLUE)📋 Step 8: Showing recent logs...$(NC)"
	@echo "$(CYAN)--- Y.js Server 1 Logs (last 5 lines) ---$(NC)"
	@docker logs yjs-server-1 --tail 5 2>/dev/null || echo "$(RED)Could not fetch yjs-server-1 logs$(NC)"
	@echo "$(CYAN)--- Y.js Server 2 Logs (last 5 lines) ---$(NC)"
	@docker logs yjs-server-2 --tail 5 2>/dev/null || echo "$(RED)Could not fetch yjs-server-2 logs$(NC)"
	@echo ""
	@echo "$(GREEN)✅ Application scaled up successfully$(NC)"
	@echo "$(BLUE)🌐 Access your application at: http://localhost:1234$(NC)"
	@echo "$(BLUE)📊 HAProxy stats at: http://localhost:8080/stats$(NC)"
	@echo "$(YELLOW)📋 Use 'make docker-logs' to follow real-time logs$(NC)"

scale-down: ## Scale down the application
	@echo "$(YELLOW)⬇️ Scaling down application...$(NC)"
	docker-compose -f docker-compose.scalable.yml down
	@echo "$(GREEN)✅ Application scaled down$(NC)"

docker-status: ## Show status of scalable setup
	@echo "$(YELLOW)📊 Scalable setup status:$(NC)"
	@docker-compose -f docker-compose.scalable.yml ps
	@echo ""
	@echo "$(CYAN)🔍 Testing load balancer:$(NC)"
	@curl -s http://localhost/health | jq . || echo "$(RED)Load balancer not responding$(NC)"
