<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y-WebSocket Realtime Collaboration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .connected { 
            background-color: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb;
        }
        .disconnected { 
            background-color: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .controls button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .btn-primary { 
            background-color: #007bff; 
            color: white; 
        }
        .btn-primary:hover { 
            background-color: #0056b3; 
        }
        .btn-secondary { 
            background-color: #6c757d; 
            color: white; 
        }
        .btn-primary:disabled { 
            background-color: #ccc; 
            cursor: not-allowed;
        }
        .editor {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s;
        }
        .editor:focus {
            outline: none;
            border-color: #007bff;
        }
        .editor:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .users {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .user {
            display: inline-block;
            margin: 3px;
            padding: 6px 12px;
            background-color: #007bff;
            color: white;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        .stats {
            margin: 15px 0;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            border: 1px solid #dee2e6;
        }
        .log {
            height: 250px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 4px;
            word-wrap: break-word;
        }
        .log-timestamp {
            color: #6c757d;
            font-weight: 500;
        }
        .log-error { color: #dc3545; }
        .log-warning { color: #fd7e14; }
        .log-success { color: #28a745; }
        h1 { color: #343a40; margin-bottom: 30px; }
        h3 { color: #495057; margin-bottom: 15px; }

        /* Tiptap Editor Styles */
        .editor-toolbar {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            flex-wrap: wrap;
        }
        .toolbar-btn {
            padding: 6px 10px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            min-width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .toolbar-btn:hover:not(:disabled) {
            background: #e9ecef;
        }
        .toolbar-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .toolbar-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .toolbar-separator {
            color: #ddd;
            margin: 0 5px;
        }
        .tiptap-editor {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }
        .tiptap-editor:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .tiptap-editor h1 {
            font-size: 2em;
            font-weight: bold;
            margin: 0.5em 0;
        }
        .tiptap-editor h2 {
            font-size: 1.5em;
            font-weight: bold;
            margin: 0.5em 0;
        }
        .tiptap-editor h3 {
            font-size: 1.2em;
            font-weight: bold;
            margin: 0.5em 0;
        }
        .tiptap-editor ul, .tiptap-editor ol {
            padding-left: 1.5em;
            margin: 0.5em 0;
        }
        .tiptap-editor blockquote {
            border-left: 4px solid #ddd;
            padding-left: 1em;
            margin: 1em 0;
            font-style: italic;
            color: #666;
        }
        .tiptap-editor pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1em;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        .tiptap-editor code {
            background: #f8f9fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .tiptap-editor p {
            margin: 0.5em 0;
        }

        /* Collaboration cursor styles */
        .collaboration-cursor__caret {
            border-left: 1px solid #0d0d0d;
            border-right: 1px solid #0d0d0d;
            margin-left: -1px;
            margin-right: -1px;
            pointer-events: none;
            position: relative;
            word-break: normal;
        }
        .collaboration-cursor__label {
            border-radius: 3px 3px 3px 0;
            color: #0d0d0d;
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            left: -1px;
            line-height: normal;
            padding: 0.1rem 0.3rem;
            position: absolute;
            top: -1.4em;
            user-select: none;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <h1>🚀 Y-WebSocket Realtime Collaboration Demo</h1>
    
    <div class="container">
        <div id="status" class="status disconnected">Disconnected - Click Connect to start</div>
        
        <div class="controls">
            <input type="text" id="documentId" value="demo-document" placeholder="Document ID" />
            <input type="text" id="userId" value="" placeholder="User ID" />
            <button id="connectBtn" class="btn-primary">Connect</button>
            <button id="disconnectBtn" class="btn-secondary" disabled>Disconnect</button>
            <button id="syncBtn" class="btn-secondary" disabled>Force Sync</button>
        </div>
        
        <div class="users">
            <strong>👥 Connected Users:</strong>
            <div id="userList">None</div>
        </div>
    </div>
    
    <div class="container">
        <h3>📝 Collaborative Text Editor (Simple)</h3>
        <p><em>Start typing below. Open this page in multiple tabs or browsers to see real-time collaboration!</em></p>
        <p><strong>Using y-websocket for better Tiptap compatibility</strong></p>
        <textarea id="editor" class="editor" placeholder="Start typing to collaborate in real-time..." disabled></textarea>
    </div>

    <div class="container">
        <h3>✨ Collaborative Rich Text Editor (Tiptap)</h3>
        <p><em>Rich text editor with formatting, lists, and real-time collaboration!</em></p>
        <div class="editor-toolbar">
            <button type="button" id="boldBtn" class="toolbar-btn" disabled title="Bold">
                <strong>B</strong>
            </button>
            <button type="button" id="italicBtn" class="toolbar-btn" disabled title="Italic">
                <em>I</em>
            </button>
            <button type="button" id="underlineBtn" class="toolbar-btn" disabled title="Underline">
                <u>U</u>
            </button>
            <button type="button" id="strikeBtn" class="toolbar-btn" disabled title="Strike">
                <s>S</s>
            </button>
            <span class="toolbar-separator">|</span>
            <button type="button" id="h1Btn" class="toolbar-btn" disabled title="Heading 1">
                H1
            </button>
            <button type="button" id="h2Btn" class="toolbar-btn" disabled title="Heading 2">
                H2
            </button>
            <button type="button" id="h3Btn" class="toolbar-btn" disabled title="Heading 3">
                H3
            </button>
            <span class="toolbar-separator">|</span>
            <button type="button" id="bulletListBtn" class="toolbar-btn" disabled title="Bullet List">
                • List
            </button>
            <button type="button" id="orderedListBtn" class="toolbar-btn" disabled title="Numbered List">
                1. List
            </button>
            <span class="toolbar-separator">|</span>
            <button type="button" id="blockquoteBtn" class="toolbar-btn" disabled title="Quote">
                " Quote
            </button>
            <button type="button" id="codeBlockBtn" class="toolbar-btn" disabled title="Code Block">
                &lt;/&gt; Code
            </button>
        </div>
        <div id="tiptapEditor" class="tiptap-editor" style="min-height: 200px; border: 1px solid #ddd; border-radius: 4px; padding: 15px; background: white;"></div>
    </div>
    
    <div class="container">
        <div class="stats">
            <strong>📊 Server Statistics:</strong>
            <div id="stats">Click Connect to load stats...</div>
        </div>
    </div>
    
    <div class="container">
        <h3>📋 Event Log</h3>
        <div id="log" class="log"></div>
    </div>

    <!-- Use our local bundle instead of CDN to avoid conflicts -->
    <script src="../yjs-bundle.js"></script>

    <!-- Tiptap Editor - Using UMD builds that work reliably -->
    <script src="https://unpkg.com/@tiptap/core@2.1.13/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@tiptap/starter-kit@2.1.13/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@tiptap/extension-underline@2.1.13/dist/index.umd.js"></script>

    <!-- Alternative: Try loading from different CDN if above fails -->
    <script>
        // Fallback loading with different approach
        if (typeof window.TiptapCore === 'undefined') {
            console.log('� Trying alternative Tiptap loading...');

            // Create script elements dynamically
            const scripts = [
                'https://unpkg.com/@tiptap/core@2.1.13/dist/index.umd.js',
                'https://unpkg.com/@tiptap/starter-kit@2.1.13/dist/index.umd.js',
                'https://unpkg.com/@tiptap/extension-underline@2.1.13/dist/index.umd.js'
            ];

            let loadedCount = 0;
            scripts.forEach((src, index) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    loadedCount++;
                    console.log(`✅ Loaded script ${index + 1}/${scripts.length}: ${src}`);
                    if (loadedCount === scripts.length) {
                        console.log('🎉 All Tiptap scripts loaded!');
                        console.log('Available globals:', Object.keys(window).filter(k => k.toLowerCase().includes('tiptap')));
                    }
                };
                script.onerror = () => {
                    console.error(`❌ Failed to load: ${src}`);
                };
                document.head.appendChild(script);
            });
        }

        // Debug what's available after a short delay and fix global access
        setTimeout(() => {
            console.log('🔍 Final check - Available globals:');
            console.log('window.TiptapCore:', typeof window.TiptapCore);
            console.log('window.TiptapStarterKit:', typeof window.TiptapStarterKit);
            console.log('window.TiptapUnderline:', typeof window.TiptapUnderline);
            console.log('All Tiptap globals:', Object.keys(window).filter(k => k.includes('Tiptap')));

            // Check for UMD globals with @ symbols
            const tiptapCore = window['@tiptap/core'];
            const starterKit = window['@tiptap/starter-kit'];
            const underline = window['@tiptap/extension-underline'];

            console.log('UMD globals:');
            console.log('@tiptap/core:', tiptapCore);
            console.log('@tiptap/starter-kit:', starterKit);
            console.log('@tiptap/extension-underline:', underline);

            // Create easier-to-access globals if UMD globals exist
            if (tiptapCore && starterKit) {
                window.TiptapCore = tiptapCore;
                window.TiptapStarterKit = starterKit;
                window.TiptapUnderline = underline;
                console.log('✅ Created accessible Tiptap globals');

                // Create a promise that resolves when rich editor is ready
                window.richEditorReady = Promise.resolve(true);
            } else {
                window.richEditorReady = Promise.resolve(false);
            }
        }, 2000);
    </script>

    <!-- Check for Tiptap availability with UMD globals -->
    <script>
        window.tiptapReady = new Promise((resolve) => {
            let checkCount = 0;
            const maxChecks = 50; // 5 seconds max

            const checkTiptap = () => {
                checkCount++;
                console.log(`🔍 Check ${checkCount}: Looking for Tiptap...`);

                // Check for UMD globals (they use @ symbols in names)
                const tiptapCore = window['@tiptap/core'];
                const starterKit = window['@tiptap/starter-kit'];
                const underline = window['@tiptap/extension-underline'];

                console.log('UMD globals check:');
                console.log('@tiptap/core:', typeof tiptapCore);
                console.log('@tiptap/starter-kit:', typeof starterKit);
                console.log('@tiptap/extension-underline:', typeof underline);

                if (tiptapCore && starterKit) {
                    console.log('✅ Tiptap UMD libraries found!');

                    // Create easier-to-access globals
                    window.TiptapCore = tiptapCore;
                    window.TiptapStarterKit = starterKit;
                    window.TiptapUnderline = underline;

                    console.log('Created accessible globals:', {
                        TiptapCore: window.TiptapCore,
                        TiptapStarterKit: window.TiptapStarterKit,
                        TiptapUnderline: window.TiptapUnderline
                    });

                    resolve(true);
                } else if (checkCount >= maxChecks) {
                    console.log('❌ Tiptap libraries not found after maximum checks');
                    console.log('Available globals:', Object.keys(window).filter(k =>
                        k.includes('tiptap') || k.includes('Tiptap') || k.includes('@tiptap')
                    ));
                    resolve(false);
                } else {
                    setTimeout(checkTiptap, 100);
                }
            };

            // Start checking after a brief delay
            setTimeout(checkTiptap, 100);
        });
    </script>
    
    <script>
        class YWebSocketClient {
            constructor() {
                this.provider = null;
                this.doc = null;
                this.text = null;
                this.connected = false;
                this.documentId = null;
                this.userId = null;
                this.users = new Set();

                // Tiptap editor properties
                this.tiptapEditor = null;
                this.tiptapText = null;
                this.awareness = null;

                this.initializeElements();
                this.setupEventListeners();
                this.generateUserId();
            }
            
            initializeElements() {
                this.statusEl = document.getElementById('status');
                this.documentIdEl = document.getElementById('documentId');
                this.userIdEl = document.getElementById('userId');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.syncBtn = document.getElementById('syncBtn');
                this.editorEl = document.getElementById('editor');
                this.userListEl = document.getElementById('userList');
                this.statsEl = document.getElementById('stats');
                this.logEl = document.getElementById('log');

                // Tiptap elements
                this.tiptapEditorEl = document.getElementById('tiptapEditor');
                this.boldBtn = document.getElementById('boldBtn');
                this.italicBtn = document.getElementById('italicBtn');
                this.underlineBtn = document.getElementById('underlineBtn');
                this.strikeBtn = document.getElementById('strikeBtn');
                this.h1Btn = document.getElementById('h1Btn');
                this.h2Btn = document.getElementById('h2Btn');
                this.h3Btn = document.getElementById('h3Btn');
                this.bulletListBtn = document.getElementById('bulletListBtn');
                this.orderedListBtn = document.getElementById('orderedListBtn');
                this.blockquoteBtn = document.getElementById('blockquoteBtn');
                this.codeBlockBtn = document.getElementById('codeBlockBtn');
            }
            
            generateUserId() {
                this.userIdEl.value = 'user-' + Math.random().toString(36).substr(2, 9);
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.syncBtn.addEventListener('click', () => this.forceSync());

                this.editorEl.addEventListener('input', (e) => {
                    // Only handle input if we're connected and have a text object
                    if (this.text) {
                        this.log(`🔍 Input event triggered`);
                        this.handleTextChange(e);
                    }
                });

                // Tiptap toolbar event listeners
                this.setupTiptapToolbarListeners();
            }

            setupTiptapToolbarListeners() {
                this.boldBtn.addEventListener('click', () => this.executeRichTextCommand('bold'));
                this.italicBtn.addEventListener('click', () => this.executeRichTextCommand('italic'));
                this.underlineBtn.addEventListener('click', () => this.executeRichTextCommand('underline'));
                this.strikeBtn.addEventListener('click', () => this.executeRichTextCommand('strikeThrough'));
                this.h1Btn.addEventListener('click', () => this.executeRichTextCommand('heading', 1));
                this.h2Btn.addEventListener('click', () => this.executeRichTextCommand('heading', 2));
                this.h3Btn.addEventListener('click', () => this.executeRichTextCommand('heading', 3));
                this.bulletListBtn.addEventListener('click', () => this.executeRichTextCommand('insertUnorderedList'));
                this.orderedListBtn.addEventListener('click', () => this.executeRichTextCommand('insertOrderedList'));
                this.blockquoteBtn.addEventListener('click', () => this.executeRichTextCommand('formatBlock', 'blockquote'));
                this.codeBlockBtn.addEventListener('click', () => this.executeRichTextCommand('formatBlock', 'pre'));
            }

            executeRichTextCommand(command, value = null) {
                if (this.tiptapEditor) {
                    // Use Tiptap commands
                    switch (command) {
                        case 'bold':
                            this.tiptapEditor.chain().focus().toggleBold().run();
                            break;
                        case 'italic':
                            this.tiptapEditor.chain().focus().toggleItalic().run();
                            break;
                        case 'underline':
                            this.tiptapEditor.chain().focus().toggleUnderline().run();
                            break;
                        case 'strikeThrough':
                            this.tiptapEditor.chain().focus().toggleStrike().run();
                            break;
                        case 'heading':
                            this.tiptapEditor.chain().focus().toggleHeading({ level: value }).run();
                            break;
                        case 'insertUnorderedList':
                            this.tiptapEditor.chain().focus().toggleBulletList().run();
                            break;
                        case 'insertOrderedList':
                            this.tiptapEditor.chain().focus().toggleOrderedList().run();
                            break;
                        case 'formatBlock':
                            if (value === 'blockquote') {
                                this.tiptapEditor.chain().focus().toggleBlockquote().run();
                            } else if (value === 'pre') {
                                this.tiptapEditor.chain().focus().toggleCodeBlock().run();
                            }
                            break;
                    }
                    this.updateTiptapToolbar();
                } else {
                    // Use browser's execCommand for simple editor
                    this.tiptapEditorEl.focus();
                    if (command === 'heading') {
                        document.execCommand('formatBlock', false, `h${value}`);
                    } else if (command === 'formatBlock') {
                        document.execCommand('formatBlock', false, value);
                    } else {
                        document.execCommand(command, false, value);
                    }
                    this.syncSimpleEditorWithYjs();
                }
            }

            handleTextChange(e) {
                const newContent = e.target.value;
                this.log(`📝 Editor input: "${newContent}" - Starting transaction`);

                // Simple approach: clear and replace content with origin tracking
                this.doc.transact(() => {
                    // Clear existing content
                    if (this.text.length > 0) {
                        this.text.delete(0, this.text.length);
                    }
                    // Insert new content
                    if (newContent.length > 0) {
                        this.text.insert(0, newContent);
                    }
                }, this.doc); // Use document object as origin to identify local changes

                this.log(`✅ Text updated in transaction: "${this.text.toString()}"`);
            }

            connect() {
                if (typeof Y === 'undefined' || typeof WebsocketProvider === 'undefined') {
                    this.log('❌ Required libraries not loaded!', 'error');
                    return;
                }
                
                const documentId = this.documentIdEl.value.trim();
                const userId = this.userIdEl.value.trim();
                
                if (!documentId || !userId) {
                    alert('Please enter both Document ID and User ID');
                    return;
                }
                
                this.documentId = documentId;
                this.userId = userId;
                
                this.log('🔌 Connecting to y-websocket server...');
                
                // Initialize YJS document
                this.doc = new Y.Doc();
                this.text = this.doc.getText('content');

                // Create WebSocket provider
                const wsUrl = `ws://localhost:3000`;
                this.log(`🔌 Connecting to: ${wsUrl}/${documentId}`);

                this.provider = new WebsocketProvider(wsUrl, documentId, this.doc);

                // Initialize Tiptap editor with Y.js collaboration (after provider is created)
                // Add a small delay to ensure Tiptap libraries are loaded
                setTimeout(() => {
                    this.initializeTiptapEditor();
                }, 1000);

                // Debug: Add raw WebSocket event listeners
                this.provider.on('connection-error', (error) => {
                    console.error('🚨 WebSocket connection error:', error);
                    this.log(`❌ Connection error: ${error.message}`, 'error');
                });

                this.provider.on('connection-close', (event) => {
                    console.log('🔌 WebSocket connection closed:', event);
                    this.log(`🔌 Connection closed: ${event.code} - ${event.reason}`, 'warning');
                });

                // Debug: Monitor the underlying WebSocket
                setTimeout(() => {
                    if (this.provider.ws) {
                        console.log('🔍 WebSocket created:', this.provider.ws);
                        console.log('🔍 WebSocket URL:', this.provider.ws.url);
                        console.log('🔍 WebSocket readyState:', this.provider.ws.readyState);

                        // Add raw WebSocket event listeners for debugging
                        this.provider.ws.addEventListener('open', (event) => {
                            console.log('🟢 Raw WebSocket OPEN:', event);
                            this.log('🟢 Raw WebSocket opened', 'success');
                        });

                        this.provider.ws.addEventListener('message', (event) => {
                            console.log('📨 Raw WebSocket MESSAGE:', event.data);
                        });

                        this.provider.ws.addEventListener('error', (event) => {
                            console.error('🔴 Raw WebSocket ERROR:', event);
                            this.log('🔴 Raw WebSocket error', 'error');
                        });

                        this.provider.ws.addEventListener('close', (event) => {
                            console.log('🔴 Raw WebSocket CLOSE:', event);
                            this.log(`🔴 Raw WebSocket closed: ${event.code}`, 'warning');
                        });
                    }
                }, 100);

                this.setupProviderListeners();
                this.setupDocumentListeners();
            }
            
            setupProviderListeners() {
                // Debug: Log the WebSocket instance
                console.log('🔍 WebSocket instance:', this.provider.ws);

                this.provider.on('status', event => {
                    this.log(`📡 Connection status: ${event.status}`);
                    console.log('🔍 WebSocket readyState:', this.provider.ws?.readyState);
                    console.log('🔍 WebSocket URL:', this.provider.ws?.url);

                    if (event.status === 'connected') {
                        this.updateConnectionStatus(true);
                        this.log('✅ Connected to y-websocket server', 'success');
                        this.startStatsUpdates();

                        // Update editor with current document content
                        setTimeout(() => {
                            this.editorEl.value = this.text.toString();
                            this.log('📄 Initial document content loaded');
                        }, 100);
                    } else if (event.status === 'disconnected') {
                        this.updateConnectionStatus(false);
                        this.log('❌ Disconnected from server', 'error');
                    }
                });

                this.provider.on('connection-error', error => {
                    this.log(`❌ Connection error: ${error.message}`, 'error');
                    this.updateConnectionStatus(false);
                });

                // Handle awareness (user presence)
                this.provider.awareness.on('change', changes => {
                    this.updateUserList();
                    this.log(`👥 Awareness updated: ${changes.added.length} added, ${changes.removed.length} removed`);
                });

                // Set local awareness state
                this.provider.awareness.setLocalStateField('user', {
                    name: this.userId,
                    color: this.generateUserColor()
                });
            }

            setupDocumentListeners() {
                // Observe text changes for real-time sync
                this.text.observe((event, transaction) => {
                    const newContent = this.text.toString();
                    this.log(`🔍 Text observer triggered. Origin: ${transaction.origin === this.doc ? 'local-doc' : 'remote'}, Content: "${newContent}"`);
                    this.log(`🔍 Current editor value: "${this.editorEl.value}"`);

                    // Only update editor if this change came from remote (not from local input)
                    if (transaction.origin !== this.doc) {
                        if (this.editorEl.value !== newContent) {
                            this.editorEl.value = newContent;
                            this.log(`📝 Text synced from remote: "${newContent}"`);
                        } else {
                            this.log(`⚠️ Content already matches: editor="${this.editorEl.value}" vs new="${newContent}"`);
                        }
                    } else {
                        this.log(`🚫 Ignoring local change (origin matches local doc)`);
                    }
                });
            }

            async initializeTiptapEditor() {
                // Wait for Tiptap libraries to load
                if (window.tiptapReady) {
                    const loaded = await window.tiptapReady;
                    if (!loaded) {
                        this.log('⚠️ Tiptap libraries failed to load, skipping rich text editor', 'warning');
                        this.createSimpleRichEditor();
                        return;
                    }
                }

                try {
                    // Check if Tiptap is available using vanilla JS approach
                    if (typeof window.TiptapCore !== 'undefined' &&
                        typeof window.TiptapStarterKit !== 'undefined') {
                        this.log('✅ Tiptap found, creating rich text editor', 'success');
                        this.createTiptapEditor();
                    } else {
                        this.log('⚠️ Tiptap not found, creating simple rich text editor', 'warning');
                        this.createSimpleRichEditor();
                    }
                } catch (error) {
                    this.log(`❌ Failed to initialize Tiptap editor: ${error.message}`, 'error');
                    console.error('Tiptap initialization error:', error);

                    // Fallback to simple rich editor
                    this.createSimpleRichEditor();
                }
            }

            createTiptapEditor() {
                try {
                    // Create a separate Y.Text for Tiptap content
                    this.tiptapText = this.doc.getText('tiptap-content');

                    // Get Tiptap classes from UMD globals
                    console.log('Available Tiptap globals:', {
                        TiptapCore: window.TiptapCore,
                        TiptapStarterKit: window.TiptapStarterKit,
                        TiptapUnderline: window.TiptapUnderline
                    });

                    // Try different ways to access the classes
                    let Editor, StarterKit, Underline;

                    if (window.TiptapCore && window.TiptapCore.Editor) {
                        Editor = window.TiptapCore.Editor;
                    } else if (window.TiptapCore && window.TiptapCore.default && window.TiptapCore.default.Editor) {
                        Editor = window.TiptapCore.default.Editor;
                    } else if (window.TiptapCore) {
                        // Sometimes the entire export is the Editor class
                        Editor = window.TiptapCore;
                    }

                    if (window.TiptapStarterKit && window.TiptapStarterKit.StarterKit) {
                        StarterKit = window.TiptapStarterKit.StarterKit;
                    } else if (window.TiptapStarterKit && window.TiptapStarterKit.default) {
                        StarterKit = window.TiptapStarterKit.default;
                    } else if (window.TiptapStarterKit) {
                        StarterKit = window.TiptapStarterKit;
                    }

                    if (window.TiptapUnderline && window.TiptapUnderline.Underline) {
                        Underline = window.TiptapUnderline.Underline;
                    } else if (window.TiptapUnderline && window.TiptapUnderline.default) {
                        Underline = window.TiptapUnderline.default;
                    } else if (window.TiptapUnderline) {
                        Underline = window.TiptapUnderline;
                    }

                    console.log('Extracted Tiptap classes:', { Editor, StarterKit, Underline });
                    console.log('Editor type:', typeof Editor);
                    console.log('StarterKit type:', typeof StarterKit);
                    console.log('Underline type:', typeof Underline);

                    // Initialize Tiptap editor
                    this.tiptapEditor = new Editor({
                        element: this.tiptapEditorEl,
                        extensions: [
                            StarterKit,
                            ...(Underline ? [Underline] : []),
                            ...(Collaboration ? [Collaboration.configure({
                                document: this.tiptapText,
                            })] : []),
                            ...(CollaborationCursor ? [CollaborationCursor.configure({
                                provider: this.provider,
                                user: {
                                    name: this.userId,
                                    color: this.generateUserColor(),
                                },
                            })] : []),
                        ],
                        content: '<p>Start typing in the rich text editor...</p>',
                        onUpdate: ({ editor }) => {
                            this.updateTiptapToolbar();
                            if (!Collaboration) {
                                this.syncTiptapWithYjs();
                            }
                            this.log('📝 Tiptap content updated');
                        },
                        onSelectionUpdate: ({ editor }) => {
                            this.updateTiptapToolbar();
                        },
                        editorProps: {
                            attributes: {
                                class: 'tiptap-editor',
                            },
                        },
                    });

                    // Set up manual Y.js sync if collaboration extension is not available
                    if (!Collaboration) {
                        this.setupTiptapYjsSync();
                    }

                    this.enableTiptapToolbar();
                    this.log('✅ Tiptap editor initialized successfully', 'success');

                } catch (error) {
                    this.log(`❌ Failed to create Tiptap editor: ${error.message}`, 'error');
                    console.error('Tiptap creation error:', error);
                    this.createSimpleRichEditor();
                }
            }

            createSimpleRichEditor() {
                this.log('🔧 Creating simple rich text editor fallback', 'warning');
                this.tiptapEditorEl.contentEditable = true;
                this.tiptapEditorEl.innerHTML = '<p>Start typing in the rich text editor (simple mode)...</p>';
                this.tiptapEditorEl.style.minHeight = '200px';
                this.tiptapEditorEl.style.padding = '15px';
                this.tiptapEditorEl.style.border = '1px solid #ddd';
                this.tiptapEditorEl.style.borderRadius = '4px';

                // Add basic event listeners
                this.tiptapEditorEl.addEventListener('input', () => {
                    this.syncSimpleEditorWithYjs();
                });

                this.enableTiptapToolbar();
                this.log('✅ Simple rich text editor created', 'success');
            }

            syncTiptapWithYjs() {
                if (!this.tiptapEditor || !this.tiptapText) return;

                const content = this.tiptapEditor.getHTML();

                // Simple sync: replace content in Y.js
                this.doc.transact(() => {
                    this.tiptapText.delete(0, this.tiptapText.length);
                    this.tiptapText.insert(0, content);
                }, 'tiptap-update');
            }

            syncSimpleEditorWithYjs() {
                if (!this.tiptapText) return;

                const content = this.tiptapEditorEl.innerHTML;

                // Simple sync: replace content in Y.js
                this.doc.transact(() => {
                    this.tiptapText.delete(0, this.tiptapText.length);
                    this.tiptapText.insert(0, content);
                }, 'simple-editor-update');
            }

            setupTiptapYjsSync() {
                if (!this.tiptapText) return;

                // Listen for changes from other clients
                this.tiptapText.observe((event, transaction) => {
                    if (transaction.origin === 'tiptap-update' || transaction.origin === 'simple-editor-update') {
                        return; // Ignore our own updates
                    }

                    const newContent = this.tiptapText.toString();

                    if (this.tiptapEditor) {
                        // Update Tiptap editor
                        this.tiptapEditor.commands.setContent(newContent);
                    } else {
                        // Update simple editor
                        this.tiptapEditorEl.innerHTML = newContent;
                    }

                    this.log('📝 Tiptap synced from remote');
                });
            }

            updateTiptapToolbar() {
                if (!this.tiptapEditor) return;

                // Update toolbar button states
                this.boldBtn.classList.toggle('active', this.tiptapEditor.isActive('bold'));
                this.italicBtn.classList.toggle('active', this.tiptapEditor.isActive('italic'));
                this.underlineBtn.classList.toggle('active', this.tiptapEditor.isActive('underline'));
                this.strikeBtn.classList.toggle('active', this.tiptapEditor.isActive('strike'));
                this.h1Btn.classList.toggle('active', this.tiptapEditor.isActive('heading', { level: 1 }));
                this.h2Btn.classList.toggle('active', this.tiptapEditor.isActive('heading', { level: 2 }));
                this.h3Btn.classList.toggle('active', this.tiptapEditor.isActive('heading', { level: 3 }));
                this.bulletListBtn.classList.toggle('active', this.tiptapEditor.isActive('bulletList'));
                this.orderedListBtn.classList.toggle('active', this.tiptapEditor.isActive('orderedList'));
                this.blockquoteBtn.classList.toggle('active', this.tiptapEditor.isActive('blockquote'));
                this.codeBlockBtn.classList.toggle('active', this.tiptapEditor.isActive('codeBlock'));
            }

            enableTiptapToolbar() {
                const toolbarButtons = [
                    this.boldBtn, this.italicBtn, this.underlineBtn, this.strikeBtn,
                    this.h1Btn, this.h2Btn, this.h3Btn,
                    this.bulletListBtn, this.orderedListBtn,
                    this.blockquoteBtn, this.codeBlockBtn
                ];

                toolbarButtons.forEach(btn => {
                    if (btn) btn.disabled = false;
                });
            }

            disableTiptapToolbar() {
                const toolbarButtons = [
                    this.boldBtn, this.italicBtn, this.underlineBtn, this.strikeBtn,
                    this.h1Btn, this.h2Btn, this.h3Btn,
                    this.bulletListBtn, this.orderedListBtn,
                    this.blockquoteBtn, this.codeBlockBtn
                ];

                toolbarButtons.forEach(btn => {
                    if (btn) {
                        btn.disabled = true;
                        btn.classList.remove('active');
                    }
                });
            }

            generateUserColor() {
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
                return colors[Math.floor(Math.random() * colors.length)];
            }
            
            disconnect() {
                if (this.provider) {
                    this.provider.destroy();
                    this.provider = null;
                }

                // Destroy Tiptap editor
                if (this.tiptapEditor) {
                    this.tiptapEditor.destroy();
                    this.tiptapEditor = null;
                    this.disableTiptapToolbar();
                    this.log('🗑️ Tiptap editor destroyed');
                }

                this.updateConnectionStatus(false);
                this.log('🔌 Disconnected by user');
            }

            forceSync() {
                if (!this.text) return;

                this.editorEl.value = this.text.toString();
                this.log('🔄 Forced sync from document state');
                console.log('Force sync - Document text:', this.text.toString());
            }
            
            updateConnectionStatus(connected) {
                this.connected = connected;
                this.statusEl.textContent = connected ? 
                    `Connected to ${this.documentId} as ${this.userId}` : 
                    'Disconnected - Click Connect to start';
                this.statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
                this.syncBtn.disabled = !connected;
                this.editorEl.disabled = !connected;
                
                if (!connected) {
                    this.users.clear();
                    this.updateUserList();
                    clearInterval(this.statsInterval);
                }
            }
            
            updateUserList() {
                if (!this.provider) {
                    this.userListEl.textContent = 'None';
                    return;
                }

                const awarenessStates = this.provider.awareness.getStates();
                this.users.clear();
                
                awarenessStates.forEach((state, clientId) => {
                    if (state.user && state.user.name) {
                        this.users.add(state.user.name);
                    }
                });

                if (this.users.size === 0) {
                    this.userListEl.textContent = 'None';
                } else {
                    this.userListEl.innerHTML = '';
                    this.users.forEach(userId => {
                        const userEl = document.createElement('span');
                        userEl.className = 'user';
                        userEl.textContent = userId;
                        this.userListEl.appendChild(userEl);
                    });
                }
            }
            
            startStatsUpdates() {
                this.fetchStats();
                this.statsInterval = setInterval(() => {
                    this.fetchStats();
                }, 5000);
            }
            
            fetchStats() {
                fetch('http://localhost:3000/api/stats')
                    .then(response => response.json())
                    .then(stats => {
                        this.statsEl.innerHTML = `
                            <div>📊 Total Connections: ${stats.connections?.totalConnections || 0}</div>
                            <div>📄 Active Documents: ${stats.documents?.totalDocuments || 0}</div>
                            <div>🕒 Last Updated: ${new Date(stats.timestamp).toLocaleTimeString()}</div>
                        `;
                    })
                    .catch(error => {
                        this.log(`Failed to fetch stats: ${error.message}`, 'warning');
                    });
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                const timestampSpan = document.createElement('span');
                timestampSpan.className = 'log-timestamp';
                timestampSpan.textContent = `[${timestamp}] `;
                
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                messageSpan.className = `log-${type}`;
                
                logEntry.appendChild(timestampSpan);
                logEntry.appendChild(messageSpan);
                
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
        }
        
        // Initialize the client after libraries load
        let client;

        function initializeClient() {
            client = new YWebSocketClient();
            client.log('🚀 Y-WebSocket Client loaded');

            // Test server
            fetch('http://localhost:3000/health')
                .then(response => response.json())
                .then(data => {
                    client.log(`✅ Server health: ${data.status}`, 'success');
                })
                .catch(error => {
                    client.log(`❌ Server not reachable: ${error.message}`, 'error');
                    client.log('Make sure the server is running: npm start', 'warning');
                });
        }

        // Initialize immediately since we're using the bundle
        if (typeof Y !== 'undefined' && typeof WebsocketProvider !== 'undefined') {
            initializeClient();
        } else {
            console.error('Y.js or WebsocketProvider not loaded from bundle');
        }

    </script>
</body>
</html>
