# Django Multi-Tenant + Vue.js SSR + Y.js Real-time Collaboration Flow

## Architecture Overview
```
User (testbank.los.com) → Django (Multi-tenant + Vue.js SSR) → Y.js WebSocket Server → Redis
```

## Complete Integration Flow

### STEP 1: User Authentication & Tenant Resolution
```
1.1 User visits: https://testbank.los.com/dashboard
1.2 Django middleware resolves tenant from subdomain "testbank"
1.3 User authenticates via Django session (existing flow)
1.4 Django stores session: {user_id: 123, tenant_id: "testbank", permissions: [...]}
```

### STEP 2: Document Access Request
```
2.1 User clicks "Edit Document" in Django-rendered Vue.js component
2.2 Vue.js component makes request to Django:
    GET /documents/doc-456/edit/
2.3 Django validates:
    - User session is valid
    - User belongs to "testbank" tenant
    - User has edit permissions for doc-456
2.4 Django renders Vue.js SSR page with document editor
```

### STEP 3: Real-time Token Generation
```javascript
// 3.1 Vue.js component (client-side) requests real-time access:
POST /api/realtime-auth/
Headers: {
  'X-CSRFToken': 'django-csrf-token',
  'Cookie': 'sessionid=django-session-id'
}
Body: {
  'document_id': 'doc-456',
  'action': 'edit'
}

// 3.2 Django validates request:
// - Check session authentication
// - Verify tenant access (testbank)
// - Confirm document permissions

// 3.3 Django generates JWT token:
Payload: {
  'user_id': 123,
  'tenant_id': 'testbank',
  'document_id': 'doc-456',
  'permissions': ['read', 'write'],
  'user_name': 'John Doe',
  'exp': timestamp + 1800,  // 30 minutes
  'iat': timestamp
}

// 3.4 Django response:
{
  'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
  'expires_in': 1800,
  'websocket_url': 'wss://testbank.los.com/realtime/doc-456',
  'document_id': 'doc-456'
}
```

### STEP 4: WebSocket Connection Establishment
```javascript
// 4.1 Vue.js Tiptap component initiates WebSocket connection:
const provider = new WebsocketProvider(
  'wss://testbank.los.com/realtime/doc-456',
  'doc-456',
  ydoc,
  {
    headers: {
      'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
    }
  }
);

// 4.2 Request hits Nginx reverse proxy (configured for WebSocket upgrade)
// 4.3 Nginx forwards to HAProxy load balancer
// 4.4 HAProxy distributes to available Y.js server instance
```

### STEP 5: Y.js Server Authorization
```javascript
// 5.1 Y.js server receives WebSocket upgrade request
// 5.2 Authorization middleware extracts JWT from headers
// 5.3 JWT validation process:
//     - Verify signature using Django secret key
//     - Check token expiration
//     - Extract user and tenant information

// 5.4 Redis cache check:
Key: auth:testbank:123:doc-456
// If cached: Use cached permissions
// If not cached: Validate and cache for 5 minutes

// 5.5 Document access validation:
//     - Ensure document belongs to "testbank" tenant
//     - Verify user has required permissions
//     - Check if document exists and is accessible

// 5.6 If authorized: Establish Y.js document collaboration
// 5.7 If denied: Close connection with error code
```

### STEP 6: Real-time Collaboration
```
6.1 Y.js server creates/joins document room: "testbank:doc-456"
6.2 User operations are validated against JWT permissions
6.3 Document changes synchronized across all connected clients
6.4 User presence and cursor positions shared
6.5 All operations logged for audit (optional)
```

### STEP 7: Permission Updates & Revocation
```javascript
// 7.1 Admin removes user permission in Django admin
// 7.2 Django publishes to Redis:
Channel: permissions:testbank:doc-456
Message: {
  'user_id': 123,
  'action': 'revoke',
  'permissions': []
}

// 7.3 Y.js servers subscribe to permission updates
// 7.4 On revocation message: Immediately disconnect user
// 7.5 Clear Redis cache for affected user
```

### STEP 8: Token Refresh Mechanism
```javascript
// 8.1 Vue.js component sets refresh timer (25 minutes)
// 8.2 Before token expiry, request new token:
POST /api/realtime-auth/refresh/
Headers: {'Authorization': 'Bearer current-token'}

// 8.3 Django validates current token and issues new one
// 8.4 Vue.js updates WebSocket provider with new token
// 8.5 Seamless continuation of collaboration session
```

### STEP 9: Session Cleanup
```
9.1 User closes browser/tab: WebSocket connection closes
9.2 Y.js server removes user from document room
9.3 Other users see user disconnect in real-time
9.4 Redis cache entries expire naturally (TTL)
9.5 Document state persisted in Y.js server storage
```

## Security Considerations

### Multi-Tenant Isolation
- All document IDs prefixed with tenant: "testbank:doc-456"
- JWT contains tenant_id for validation
- Redis keys namespaced by tenant
- Cross-tenant access impossible

### Permission Enforcement
- Document-level permissions in JWT
- Real-time permission updates via Redis pub/sub
- Operation-level validation in Y.js server
- Audit logging for compliance

### Token Security
- 30-minute expiration with refresh mechanism
- Signed with Django secret key
- HTTPS/WSS only transport
- No token storage in localStorage (use memory)

### WebSocket Security
- Origin validation against tenant domain
- Rate limiting per user/tenant
- Connection limits per document
- DDoS protection via Nginx/HAProxy

## Infrastructure Components

### Nginx Configuration
- SSL termination for testbank.los.com
- WebSocket upgrade support
- Reverse proxy to HAProxy
- Static file serving for Vue.js assets

### HAProxy Load Balancer
- WebSocket-aware load balancing
- Health checks for Y.js servers
- Sticky sessions for document rooms
- Statistics dashboard

### Redis Cluster
- Permission caching (5-minute TTL)
- Real-time permission updates (pub/sub)
- Y.js document state storage
- Session data sharing

### Y.js Servers
- Horizontal scaling (2+ instances)
- JWT validation middleware
- Redis integration for shared state
- Document persistence and sync

### Django Application
- Multi-tenant session management
- JWT token generation/validation
- Permission management API
- Vue.js SSR integration

## Deployment Architecture

### Production Setup
```
Internet → Cloudflare → Nginx → HAProxy → Y.js Servers
                               → Django App
                               → Redis Cluster
```

### Development Setup
```
localhost:3000 (Vue.js/Django) → localhost:1234 (HAProxy) → Y.js Servers
                                                         → Redis
```

### Monitoring
- HAProxy stats: http://testbank.los.com:8080/stats
- Redis monitoring: RedisInsight
- Application logs: ELK stack
- Performance: New Relic/DataDog

## Implementation Checklist

### Django Backend Implementation
```python
# [ ] 1. Add JWT token generation endpoint
# urls.py
path('api/realtime-auth/', RealtimeAuthView.as_view(), name='realtime-auth'),
path('api/realtime-auth/refresh/', RealtimeTokenRefreshView.as_view(), name='realtime-refresh'),

# views.py
class RealtimeAuthView(APIView):
    def post(self, request):
        # Validate session and tenant
        # Generate JWT with user/tenant/document permissions
        # Return token and WebSocket URL

# [ ] 2. Implement permission validation
# permissions.py
class DocumentPermissionValidator:
    def validate_user_document_access(self, user, tenant, document_id, action):
        # Check user belongs to tenant
        # Verify document permissions
        # Return permission level

# [ ] 3. Add Redis pub/sub for permission updates
# signals.py
@receiver(post_save, sender=DocumentPermission)
def publish_permission_change(sender, instance, **kwargs):
    # Publish to Redis channel: permissions:{tenant}:{document_id}
    # Message: {user_id, action, permissions}

# [ ] 4. Create document access control middleware
# middleware.py
class TenantDocumentMiddleware:
    def process_request(self, request):
        # Extract tenant from subdomain
        # Validate document access for tenant
```

### Vue.js Frontend Implementation
```javascript
// [ ] 1. Integrate Tiptap editor with Y.js
// TiptapEditor.vue
<template>
  <div ref="editor"></div>
</template>

<script>
import { Editor } from '@tiptap/vue-3'
import { WebsocketProvider } from 'y-websocket'
import * as Y from 'yjs'

export default {
  props: ['documentId'],
  async mounted() {
    // Request real-time token
    const token = await this.getRealtimeToken()
    // Initialize Y.js document and WebSocket provider
    this.initializeCollaboration(token)
  },
  methods: {
    async getRealtimeToken() {
      // POST /api/realtime-auth/
    },
    initializeCollaboration(token) {
      // Setup WebSocket provider with JWT
    }
  }
}
</script>

// [ ] 2. Add token request functionality
// api/realtime.js
export async function requestRealtimeToken(documentId, action = 'edit') {
  const response = await fetch('/api/realtime-auth/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': getCsrfToken()
    },
    body: JSON.stringify({ document_id: documentId, action })
  })
  return response.json()
}

// [ ] 3. Implement WebSocket connection management
// composables/useCollaboration.js
export function useCollaboration(documentId) {
  const provider = ref(null)
  const isConnected = ref(false)
  const userCount = ref(0)

  const connect = async () => {
    const { token, websocket_url } = await requestRealtimeToken(documentId)
    provider.value = new WebsocketProvider(websocket_url, documentId, ydoc, {
      headers: { 'Authorization': `Bearer ${token}` }
    })
  }

  return { connect, isConnected, userCount }
}

// [ ] 4. Handle token refresh and error states
// utils/tokenManager.js
class TokenManager {
  constructor() {
    this.refreshTimer = null
  }

  scheduleRefresh(expiresIn) {
    // Set timer for 25 minutes (5 min before expiry)
    this.refreshTimer = setTimeout(() => {
      this.refreshToken()
    }, (expiresIn - 300) * 1000)
  }

  async refreshToken() {
    // Request new token and update WebSocket provider
  }
}
```

### Y.js Server Implementation
```javascript
// [ ] 1. Add JWT validation middleware
// middleware/auth.js
const jwt = require('jsonwebtoken')
const redis = require('redis')

class AuthMiddleware {
  async validateConnection(request) {
    const token = this.extractToken(request.headers)
    const payload = jwt.verify(token, process.env.DJANGO_SECRET_KEY)

    // Check Redis cache
    const cacheKey = `auth:${payload.tenant_id}:${payload.user_id}:${payload.document_id}`
    let permissions = await redis.get(cacheKey)

    if (!permissions) {
      // Validate with Django API (fallback)
      permissions = await this.validateWithDjango(payload)
      await redis.setex(cacheKey, 300, JSON.stringify(permissions))
    }

    return { ...payload, permissions: JSON.parse(permissions) }
  }
}

// [ ] 2. Implement Redis caching for permissions
// services/RedisService.js
class RedisService {
  constructor() {
    this.client = redis.createClient(process.env.REDIS_URL)
    this.subscriber = redis.createClient(process.env.REDIS_URL)
  }

  async cachePermissions(tenantId, userId, documentId, permissions) {
    const key = `auth:${tenantId}:${userId}:${documentId}`
    await this.client.setex(key, 300, JSON.stringify(permissions))
  }

  subscribeToPermissionUpdates(callback) {
    this.subscriber.psubscribe('permissions:*')
    this.subscriber.on('pmessage', callback)
  }
}

// [ ] 3. Add real-time permission update handling
// handlers/PermissionHandler.js
class PermissionHandler {
  constructor(redisService) {
    this.redisService = redisService
    this.setupSubscriptions()
  }

  setupSubscriptions() {
    this.redisService.subscribeToPermissionUpdates((pattern, channel, message) => {
      const { user_id, action, permissions } = JSON.parse(message)

      if (action === 'revoke') {
        this.disconnectUser(user_id)
      } else if (action === 'update') {
        this.updateUserPermissions(user_id, permissions)
      }
    })
  }

  disconnectUser(userId) {
    // Find and close WebSocket connections for user
  }
}

// [ ] 4. Create document access control
// controllers/DocumentController.js
class DocumentController {
  async handleDocumentOperation(userId, documentId, operation) {
    const permissions = await this.getUserPermissions(userId, documentId)

    if (!this.hasPermission(operation.type, permissions)) {
      throw new Error('Insufficient permissions')
    }

    // Process the operation
    return this.processOperation(operation)
  }

  hasPermission(operationType, permissions) {
    const requiredPermission = operationType === 'read' ? 'read' : 'write'
    return permissions.includes(requiredPermission)
  }
}
```

### Infrastructure Setup
```bash
# [ ] 1. Configure Nginx for WebSocket proxying
# /etc/nginx/sites-available/testbank.los.com
server {
    listen 443 ssl;
    server_name testbank.los.com;

    location /realtime/ {
        proxy_pass http://haproxy:1234;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        proxy_pass http://django:8000;
    }
}

# [ ] 2. Set up HAProxy load balancing
# haproxy.cfg - already configured in your setup

# [ ] 3. Deploy Redis cluster
# docker-compose.yml - Redis already configured

# [ ] 4. Configure SSL certificates
# Use Let's Encrypt or your SSL provider
```

### Security Implementation
```bash
# [ ] 1. Implement rate limiting
# Already configured in Nginx

# [ ] 2. Add audit logging
# Log all document operations with user/tenant context

# [ ] 3. Set up monitoring and alerting
# Configure alerts for failed authentications, high error rates

# [ ] 4. Conduct security testing
# Test JWT validation, permission enforcement, tenant isolation
```

## Next Steps Priority Order

1. **Django JWT Implementation** - Start with token generation endpoint
2. **Vue.js Integration** - Add Tiptap editor with Y.js WebSocket
3. **Y.js Server Auth** - Implement JWT validation middleware
4. **Redis Integration** - Add permission caching and pub/sub
5. **Testing & Security** - Comprehensive testing of multi-tenant isolation
6. **Production Deployment** - SSL, monitoring, and performance optimization

This flow ensures secure, scalable real-time collaboration while maintaining your existing Django multi-tenant architecture with Vue.js SSR.
