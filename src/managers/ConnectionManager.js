import IConnectionManager from '../interfaces/IConnectionManager.js';
import { docs } from '../utils/y-websocket-utils.js';

class ConnectionManager extends IConnectionManager {
  constructor(logger) {
    super();
    this.logger = logger;
    this.connections = new Map();
    this.documentConnections = new Map();
  }

  addConnection(connectionId, ws, metadata = {}) {
    try {
      const connection = {
        id: connectionId,
        ws: ws,
        documentId: metadata.documentId,
        userId: metadata.userId,
        joinedAt: new Date(),
        lastActivity: new Date(),
        ...metadata
      };

      this.connections.set(connectionId, connection);

      if (connection.documentId) {
        if (!this.documentConnections.has(connection.documentId)) {
          this.documentConnections.set(connection.documentId, new Set());
        }
        this.documentConnections.get(connection.documentId).add(connectionId);
      }

      return connection;
    } catch (error) {
      this.logger.error('Failed to add connection', error, { connectionId });
      throw error;
    }
  }

  removeConnection(connectionId) {
    try {
      const connection = this.connections.get(connectionId);
      if (!connection) {
        return false;
      }

      if (connection.documentId) {
        const docConnections = this.documentConnections.get(connection.documentId);
        if (docConnections) {
          docConnections.delete(connectionId);
          if (docConnections.size === 0) {
            this.documentConnections.delete(connection.documentId);
          }
        }
      }

      this.connections.delete(connectionId);
      return true;
    } catch (error) {
      this.logger.error('Failed to remove connection', error, { connectionId });
      throw error;
    }
  }

  getConnection(connectionId) {
    return this.connections.get(connectionId) || null;
  }

  getConnectionsByDocument(documentId) {
    const doc = docs.get(documentId);
    if (!doc) return [];

    const connections = [];
    doc.conns.forEach((_, ws) => {
      const connectionId = this.findConnectionIdByWs(ws);
      const connection = connectionId ? this.connections.get(connectionId) : null;

      if (connection) {
        connections.push(connection);
      } else {
        connections.push({
          id: `ws-${Date.now()}-${Math.random()}`,
          ws: ws,
          documentId: documentId,
          joinedAt: new Date(),
          lastActivity: new Date()
        });
      }
    });

    return connections;
  }

  findConnectionIdByWs(ws) {
    for (const [connectionId, connection] of this.connections) {
      if (connection.ws === ws) {
        return connectionId;
      }
    }
    return null;
  }

  getConnectionCount() {
    return this.connections.size;
  }

  broadcast(documentId, message, excludeConnectionId = null) {
    try {
      const connections = this.getConnectionsByDocument(documentId);
      let broadcastCount = 0;

      connections.forEach(connection => {
        if (connection.id !== excludeConnectionId && connection.ws) {
          try {
            if (connection.ws.readyState === 1) {
              connection.ws.send(JSON.stringify(message));
              broadcastCount++;
            }
          } catch (error) {
            this.logger.warn('Failed to broadcast to connection', error, {
              connectionId: connection.id
            });
          }
        }
      });

      return broadcastCount;
    } catch (error) {
      this.logger.error('Failed to broadcast message', error, { documentId });
      throw error;
    }
  }

  updateLastActivity(connectionId) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastActivity = new Date();
    }
  }

  getConnectionStats() {
    const stats = {
      totalConnections: this.connections.size,
      documentsWithConnections: this.documentConnections.size,
      connectionsByDocument: {}
    };

    this.documentConnections.forEach((connections, documentId) => {
      stats.connectionsByDocument[documentId] = connections.size;
    });

    return stats;
  }
}

export default ConnectionManager;
