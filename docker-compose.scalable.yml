# 🚀 Scalable Real-time Y.js Server with Redis
# This setup supports horizontal scaling with multiple server instances

version: '3.8'

services:
  # Redis for shared state and pub/sub
  redis:
    image: redis:7-alpine
    container_name: yjs-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - yjs-network

  # Load Balancer (HAProxy)
  loadbalancer:
    image: haproxy:2.8-alpine
    container_name: yjs-loadbalancer
    ports:
      - "1234:1234"  # Main entry point
      - "8080:8080"  # HAProxy stats
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    depends_on:
      - yjs-server-1
      - yjs-server-2
    restart: unless-stopped
    networks:
      - yjs-network

  # Y.js Server Instance 1
  yjs-server-1:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yjs-server-1
    environment:
      - NODE_ENV=production
      - PORT=1234
      - HOST=0.0.0.0
      - REDIS_URL=redis://redis:6379
      - REDIS_ENABLED=true
      - INSTANCE_ID=server-1
      - LOG_LEVEL=info
      - CORS_ORIGIN=*
      - YJS_PERSISTENCE=redis
      - YJS_GC_ENABLED=true
      - WS_PING_TIMEOUT=60000
      - WS_PING_INTERVAL=25000
      - CLUSTER_MODE=true
    volumes:
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:1234/health', (res) => { if (res.statusCode === 200) process.exit(0); else process.exit(1); }).on('error', () => process.exit(1));"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yjs-network

  # Y.js Server Instance 2
  yjs-server-2:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yjs-server-2
    environment:
      - NODE_ENV=production
      - PORT=1234
      - HOST=0.0.0.0
      - REDIS_URL=redis://redis:6379
      - REDIS_ENABLED=true
      - INSTANCE_ID=server-2
      - LOG_LEVEL=info
      - CORS_ORIGIN=*
      - YJS_PERSISTENCE=redis
      - YJS_GC_ENABLED=true
      - WS_PING_TIMEOUT=60000
      - WS_PING_INTERVAL=25000
      - CLUSTER_MODE=true
    volumes:
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:1234/health', (res) => { if (res.statusCode === 200) process.exit(0); else process.exit(1); }).on('error', () => process.exit(1));"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yjs-network

  # Y.js Server Instance 3 (can be scaled up/down)
  yjs-server-3:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yjs-server-3
    environment:
      - NODE_ENV=production
      - PORT=1234
      - HOST=0.0.0.0
      - REDIS_URL=redis://redis:6379
      - REDIS_ENABLED=true
      - INSTANCE_ID=server-3
      - LOG_LEVEL=info
      - CORS_ORIGIN=*
      - YJS_PERSISTENCE=redis
      - YJS_GC_ENABLED=true
      - WS_PING_TIMEOUT=60000
      - WS_PING_INTERVAL=25000
      - CLUSTER_MODE=true
    volumes:
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:1234/health', (res) => { if (res.statusCode === 200) process.exit(0); else process.exit(1); }).on('error', () => process.exit(1));"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yjs-network
    profiles:
      - scale-up

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: yjs-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - yjs-network
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: yjs-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - yjs-network
    profiles:
      - monitoring

  # Redis Commander for Redis management (optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: yjs-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - yjs-network
    profiles:
      - tools

networks:
  yjs-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  logs:
    driver: local
