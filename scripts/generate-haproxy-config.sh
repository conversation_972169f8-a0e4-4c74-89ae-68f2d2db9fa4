#!/bin/bash

# Generate HAProxy configuration from environment variables
# This script replaces hardcoded values with environment variables

set -e

# Load environment variables from .env if it exists
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Set default values
HAPROXY_MAX_CONN=${HAPROXY_MAX_CONN:-4096}
HAPROXY_RETRIES=${HAPROXY_RETRIES:-3}
HAPROXY_TIMEOUT_CONNECT=${HAPROXY_TIMEOUT_CONNECT:-5000}
HAPROXY_TIMEOUT_CLIENT=${HAPROXY_TIMEOUT_CLIENT:-50000}
HAPROXY_TIMEOUT_SERVER=${HAPROXY_TIMEOUT_SERVER:-50000}
HAPROXY_TIMEOUT_TUNNEL=${HAPROXY_TIMEOUT_TUNNEL:-3600000}
HAPROXY_TIMEOUT_HTTP_KEEP_ALIVE=${HAPROXY_TIMEOUT_HTTP_KEEP_ALIVE:-10000}
HAPROXY_TIMEOUT_HTTP_REQUEST=${HAPROXY_TIMEOUT_HTTP_REQUEST:-10000}

# Generate HAProxy configuration
cat > haproxy.cfg << EOF
# HAProxy Configuration for Y.js WebSocket Load Balancing
# Supports sticky sessions and WebSocket upgrades

global
    daemon
    log stdout local0 info
    maxconn ${HAPROXY_MAX_CONN}
    user haproxy
    group haproxy

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option redispatch
    retries ${HAPROXY_RETRIES}
    timeout connect ${HAPROXY_TIMEOUT_CONNECT}ms
    timeout client ${HAPROXY_TIMEOUT_CLIENT}ms
    timeout server ${HAPROXY_TIMEOUT_SERVER}ms
    timeout tunnel ${HAPROXY_TIMEOUT_TUNNEL}ms
    timeout http-keep-alive ${HAPROXY_TIMEOUT_HTTP_KEEP_ALIVE}ms
    timeout http-request ${HAPROXY_TIMEOUT_HTTP_REQUEST}ms

# Frontend for incoming connections
frontend yjs_frontend
    bind *:1234
    
    # WebSocket upgrade detection
    acl is_websocket hdr(Upgrade) -i websocket
    acl is_websocket_connection hdr_beg(Connection) -i upgrade
    
    # Route WebSocket connections to backend
    use_backend yjs_websocket_backend if is_websocket is_websocket_connection
    
    # Route HTTP requests to backend
    default_backend yjs_http_backend

# Backend for WebSocket connections (sticky sessions)
backend yjs_websocket_backend
    balance source
    hash-type consistent
    
    # Health check for WebSocket
    option httpchk GET /health
    
    # Y.js server instances
    server yjs1 yjs-server-1:1234 check
    server yjs2 yjs-server-2:1234 check

# Backend for HTTP requests (round-robin)
backend yjs_http_backend
    balance roundrobin
    
    # Health check for HTTP
    option httpchk GET /health
    
    # Y.js server instances
    server yjs1 yjs-server-1:1234 check
    server yjs2 yjs-server-2:1234 check

# Statistics page
listen stats
    bind *:8080
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
EOF

echo "✅ HAProxy configuration generated successfully with environment variables"
echo "📁 Configuration saved to: haproxy.cfg"
echo ""
echo "🔧 Current configuration values:"
echo "   Max Connections: ${HAPROXY_MAX_CONN}"
echo "   Retries: ${HAPROXY_RETRIES}"
echo "   Connect Timeout: ${HAPROXY_TIMEOUT_CONNECT}ms"
echo "   Client Timeout: ${HAPROXY_TIMEOUT_CLIENT}ms"
echo "   Server Timeout: ${HAPROXY_TIMEOUT_SERVER}ms"
echo "   Tunnel Timeout: ${HAPROXY_TIMEOUT_TUNNEL}ms"
