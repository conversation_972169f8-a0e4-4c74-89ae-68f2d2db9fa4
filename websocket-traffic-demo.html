<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Traffic Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step {
            background: #e3f2fd;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
            border-radius: 4px;
        }
        .network-entry {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .websocket-frame {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px;
            margin: 3px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .traffic-log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instruction {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🌐 WebSocket Traffic Visualization</h1>
    
    <div class="instruction">
        <h3>📋 How to See WebSocket Traffic in DevTools:</h3>
        <ol>
            <li><strong>Open DevTools FIRST</strong> (F12) before connecting</li>
            <li>Go to <strong>Network</strong> tab</li>
            <li>Click <strong>"Connect"</strong> below</li>
            <li>Look for the WebSocket connection (Status: 101)</li>
            <li><strong>Click on the WebSocket entry</strong></li>
            <li>Click the <strong>"Messages"</strong> tab to see frames</li>
        </ol>
    </div>
    
    <div class="container">
        <h3>🔌 Connection Control</h3>
        <div class="controls">
            <button id="connectBtn" onclick="connectWebSocket()">Connect WebSocket</button>
            <button id="sendBtn" onclick="sendMessage()" disabled>Send Message</button>
            <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>Disconnect</button>
        </div>
        <div id="status">Status: Disconnected</div>
    </div>
    
    <div class="container">
        <h3>📊 What You See in Network Tab</h3>
        
        <div class="step">
            <strong>Step 1: Initial HTTP Request (Handshake)</strong>
            <div class="network-entry">
                <div>Request URL: <span class="highlight">ws://localhost:3000/demo-doc</span></div>
                <div>Request Method: GET</div>
                <div>Status Code: <span class="highlight">101 Switching Protocols</span></div>
                <div>Connection: Upgrade</div>
                <div>Upgrade: websocket</div>
            </div>
        </div>
        
        <div class="step">
            <strong>Step 2: WebSocket Frames (Click on the 101 entry → Messages tab)</strong>
            <div id="frameLog">
                <div class="websocket-frame">⬇️ Incoming: Binary frame (Y.js sync data) - 45 bytes</div>
                <div class="websocket-frame">⬆️ Outgoing: Binary frame (Y.js awareness) - 23 bytes</div>
                <div class="websocket-frame">⬇️ Incoming: Binary frame (Document update) - 67 bytes</div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h3>📡 Live Traffic Monitor</h3>
        <div id="trafficLog" class="traffic-log">Waiting for WebSocket connection...
        
🔍 TIP: Open DevTools → Network → Look for Status 101 entry → Click it → Messages tab</div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        
        function log(message) {
            const logEl = document.getElementById('trafficLog');
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `\n[${timestamp}] ${message}`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateStatus(status) {
            document.getElementById('status').textContent = `Status: ${status}`;
        }
        
        function connectWebSocket() {
            log('🔌 Initiating WebSocket connection...');
            log('📋 CHECK DEVTOOLS: Network tab → Look for Status 101');
            
            ws = new WebSocket('ws://localhost:3000/demo-doc');
            
            ws.onopen = function(event) {
                log('✅ WebSocket connection established');
                log('📋 NOW: Click the 101 entry in Network tab → Messages tab');
                updateStatus('Connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('sendBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = false;
                
                // Add frame to visual log
                addFrame('⬇️ Connection established frame', 'system');
            };
            
            ws.onmessage = function(event) {
                messageCount++;
                const size = event.data.length || event.data.byteLength || 0;
                log(`📨 Received message #${messageCount} (${size} bytes)`);
                log('📋 SEE THIS: DevTools → Network → 101 entry → Messages tab');
                
                // Add frame to visual log
                addFrame(`⬇️ Incoming: Data frame - ${size} bytes`, 'incoming');
            };
            
            ws.onerror = function(error) {
                log('❌ WebSocket error occurred');
                console.error('WebSocket error:', error);
            };
            
            ws.onclose = function(event) {
                log(`🔌 WebSocket closed (Code: ${event.code})`);
                updateStatus('Disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('sendBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = true;
            };
        }
        
        function sendMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = `Test message ${Date.now()}`;
                ws.send(message);
                log(`📤 Sent: "${message}"`);
                log('📋 SEE THIS: DevTools → Network → 101 entry → Messages tab');
                
                // Add frame to visual log
                addFrame(`⬆️ Outgoing: "${message}" - ${message.length} bytes`, 'outgoing');
            }
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                log('🔌 Disconnecting WebSocket...');
            }
        }
        
        function addFrame(frameText, type) {
            const frameLog = document.getElementById('frameLog');
            const frame = document.createElement('div');
            frame.className = 'websocket-frame';
            frame.textContent = frameText;
            frameLog.appendChild(frame);
        }
        
        // Initial log
        log('🚀 WebSocket Traffic Demo loaded');
        log('📋 IMPORTANT: Open DevTools (F12) → Network tab BEFORE connecting');
        log('💡 WebSocket data appears as "frames" within the 101 connection entry');
    </script>
</body>
</html>
