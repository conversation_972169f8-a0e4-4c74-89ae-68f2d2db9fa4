{"name": "realtime-yjs-server", "version": "1.0.0", "description": "A real-time collaborative server using YJS and y-websocket with SOLID principles", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "validate-config": "node scripts/validate-config.js", "generate-haproxy": "bash scripts/generate-haproxy-config.sh", "docker:build": "docker build -t realtime-yjs-server .", "docker:run": "docker run -p 3000:3000 realtime-yjs-server"}, "keywords": ["yjs", "y-websocket", "realtime", "collaboration", "websocket", "nodejs"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "helmet": "^7.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lib0": "^0.2.109", "node-fetch": "^3.3.2", "winston": "^3.11.0", "ws": "^8.18.3", "y-websocket": "^1.5.0", "yjs": "^13.6.27"}, "devDependencies": {"esbuild": "^0.25.5", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}