class IConnectionManager {
  addConnection(connectionId, socket, metadata = {}) {
    throw new Error('Method addConnection must be implemented');
  }

  removeConnection(connectionId) {
    throw new Error('Method removeConnection must be implemented');
  }

  getConnection(connectionId) {
    throw new Error('Method getConnection must be implemented');
  }

  getConnectionsByDocument(documentId) {
    throw new Error('Method getConnectionsByDocument must be implemented');
  }

  getConnectionCount() {
    throw new Error('Method getConnectionCount must be implemented');
  }

  broadcast(documentId, message, excludeConnectionId = null) {
    throw new Error('Method broadcast must be implemented');
  }
}

export default IConnectionManager;
