# 📊 Logging and Monitoring Guide

## Overview

This guide covers the comprehensive logging and monitoring features added to the Real-time Y.js WebSocket Server. The system now provides detailed insights into authentication, scaling operations, and system health.

## 🚀 Enhanced Scale-Up Logging

### Command: `make scale-up`

The scale-up process now includes detailed step-by-step logging:

```bash
make scale-up
```

**What it shows:**
- ✅ **Step 1**: Docker container startup progress
- ✅ **Step 2**: Service initialization wait time
- ✅ **Step 3**: Container status overview
- ✅ **Step 4**: Redis connection testing
- ✅ **Step 5**: Individual Y.js server health checks
- ✅ **Step 6**: HAProxy load balancer testing
- ✅ **Step 7**: Authentication status verification
- ✅ **Step 8**: Recent logs from all services

**Example Output:**
```
🚀 Scaling up application...
📋 Step 1: Starting Docker containers...
📋 Step 2: Waiting for services to initialize...
📋 Step 3: Checking container status...
📋 Step 4: Testing Redis connection...
    ✅ Redis is responding
📋 Step 5: Testing Y.js server instances...
    ✅ yjs-server-1 is healthy
    ✅ yjs-server-2 is healthy
📋 Step 6: Testing HAProxy load balancer...
    ✅ HAProxy load balancer is working
📋 Step 7: Checking authentication status...
    ✅ JWT Authentication: ENABLED
    💡 Use 'npm run generate-demo-token' to create test tokens
📋 Step 8: Showing recent logs...
```

## 🔐 Authentication Monitoring

### Command: `make auth-status`

Comprehensive authentication status across all services:

```bash
make auth-status
```

**Features:**
- Environment configuration verification
- Individual server authentication status
- Load balancer authentication status
- Token generation guidance

**Example Output:**
```
🔐 Authentication Status Check:

📋 Environment Configuration:
  AUTH_ENABLED in .env: true

📋 Server Status:
  Testing yjs-server-1:
    Status: ✅ Running
    Auth Enabled: true
    Last Check: 2025-07-06T09:41:05.676Z

  Testing yjs-server-2:
    Status: ✅ Running
    Auth Enabled: true
    Last Check: 2025-07-06T09:41:05.678Z

📋 Load Balancer Status:
  Load Balancer: ✅ Running
  Auth Enabled: true
  💡 Generate test token: npm run generate-demo-token
```

### Command: `make auth-logs`

Real-time authentication event monitoring:

```bash
make auth-logs
```

**What it shows:**
- Authentication initialization events
- Token validation attempts
- Connection success/failure events
- Permission updates
- User authentication context

**Example Log Events:**
```
yjs-server-1 | info: Authentication initialized successfully {"enabled":true}
yjs-server-2 | warn: No authentication token provided {"origin":"http://localhost:3003","url":"/demo-document"}
yjs-server-2 | warn: WebSocket authentication failed {"code":"MISSING_TOKEN","error":"No authentication token provided"}
yjs-server-1 | info: 🔐 WebSocket connection authenticated successfully {"userId":"492","tenantId":"demo-tenant","documentId":"demo-document"}
```

## 📋 General Logging Commands

### Command: `make docker-logs`

Enhanced Docker logs with user guidance:

```bash
make docker-logs
```

**Features:**
- Clear instructions for stopping logs
- Service identification
- Real-time log following

### Command: `make docker-status`

Detailed system status overview:

```bash
make docker-status
```

**Shows:**
- Container status and health
- Load balancer connectivity test
- Service availability verification

## 🔍 Authentication Log Types

### 1. Initialization Logs
```json
{
  "level": "info",
  "message": "Authentication initialized successfully",
  "enabled": true,
  "service": "realtime-yjs-server",
  "timestamp": "2025-07-06T09:41:05.676Z"
}
```

### 2. Authentication Success Logs
```json
{
  "level": "info", 
  "message": "🔐 WebSocket connection authenticated successfully",
  "userId": "492",
  "tenantId": "demo-tenant", 
  "documentId": "demo-document",
  "permissions": ["read", "write"],
  "userName": "Demo User 492",
  "origin": "http://localhost:3003",
  "tokenExpiry": "2025-07-07T09:41:05.000Z"
}
```

### 3. Authentication Failure Logs
```json
{
  "level": "warn",
  "message": "🔒 No authentication token provided - connection rejected",
  "url": "/demo-document",
  "origin": "http://localhost:3003", 
  "userAgent": "Mozilla/5.0...",
  "method": "WebSocket upgrade",
  "code": "MISSING_TOKEN"
}
```

### 4. Permission Update Logs
```json
{
  "level": "info",
  "message": "User permissions updated",
  "userId": "492",
  "documentId": "demo-document", 
  "newPermissions": ["read"]
}
```

## 🛠️ Troubleshooting with Logs

### Issue: Authentication Not Working

1. **Check Environment Configuration:**
   ```bash
   make auth-status
   ```

2. **Monitor Authentication Events:**
   ```bash
   make auth-logs
   ```

3. **Generate Test Token:**
   ```bash
   npm run generate-demo-token
   ```

### Issue: Scale-Up Problems

1. **Run Enhanced Scale-Up:**
   ```bash
   make scale-up
   ```
   
2. **Check Individual Services:**
   - Look for failed health checks in Step 5
   - Verify Redis connectivity in Step 4
   - Check authentication status in Step 7

### Issue: Connection Failures

1. **Monitor Real-Time Logs:**
   ```bash
   make auth-logs
   ```

2. **Look for specific error codes:**
   - `MISSING_TOKEN`: No authentication token provided
   - `INVALID_TOKEN`: Token validation failed
   - `ACCESS_DENIED`: Insufficient permissions
   - `AUTH_ERROR`: General authentication error

## 📊 Log Analysis Tips

### Authentication Patterns
- **Guest Connections**: Look for `"isAuthenticated":false` and `"userId":"guest"`
- **Token Failures**: Search for `MISSING_TOKEN` or `INVALID_TOKEN` codes
- **Successful Auth**: Look for `🔐 WebSocket connection authenticated successfully`

### Performance Monitoring
- **Connection Frequency**: Monitor authentication attempt frequency
- **Token Expiry**: Watch for token expiration patterns
- **Load Distribution**: Check which server instances handle requests

### Security Monitoring
- **Failed Attempts**: Monitor `warn` level authentication failures
- **Origin Tracking**: Check `origin` field for unexpected sources
- **Permission Changes**: Watch for permission update events

## 🎯 Best Practices

1. **Regular Monitoring**: Use `make auth-status` for periodic health checks
2. **Real-Time Debugging**: Use `make auth-logs` during development
3. **Scale-Up Verification**: Always check all 8 steps during deployment
4. **Log Retention**: Consider log rotation for production environments
5. **Alert Setup**: Monitor for authentication failure spikes

## 🔧 Configuration

All logging features respect the environment variables:
- `AUTH_ENABLED`: Controls authentication system
- `LOG_LEVEL`: Controls log verbosity
- `JWT_*`: JWT configuration affects authentication logs

The logging system is designed to provide maximum visibility into the authentication and scaling processes while maintaining security best practices.
