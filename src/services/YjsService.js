class YjsService {
  constructor(connection<PERSON>anager, document<PERSON>anager, webSocketHandler, logger) {
    this.connectionManager = connectionManager;
    this.documentManager = documentManager;
    this.webSocketHandler = webSocketHandler;
    this.logger = logger;
  }

  initialize() {
    this.setupPeriodicCleanup();
  }

  setupPeriodicCleanup() {
    const cleanupInterval = 5 * 60 * 1000;

    setInterval(() => {
      try {
        const cleanedCount = this.documentManager.cleanup();
        if (cleanedCount > 0) {
          this.logger.info('Periodic cleanup completed', { cleanedCount });
        }
      } catch (error) {
        this.logger.error('Periodic cleanup failed', error);
      }
    }, cleanupInterval);
  }

  getStats() {
    try {
      const connectionStats = this.connectionManager.getConnectionStats();
      const documentStats = this.documentManager.getOverallStats();

      return {
        connections: connectionStats,
        documents: documentStats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Failed to get service stats', error);
      throw error;
    }
  }

  getDocumentInfo(documentId) {
    try {
      const documentStats = this.documentManager.getDocumentStats(documentId);
      const connections = this.connectionManager.getConnectionsByDocument(documentId);

      return {
        documentId,
        stats: documentStats,
        connections: connections.map(conn => ({
          id: conn.id,
          userId: conn.userId,
          joinedAt: conn.joinedAt,
          lastActivity: conn.lastActivity
        }))
      };
    } catch (error) {
      this.logger.error('Failed to get document info', error, { documentId });
      throw error;
    }
  }

  cleanupDocument(documentId) {
    try {
      const connections = this.connectionManager.getConnectionsByDocument(documentId);

      if (connections.length > 0) {
        throw new Error('Cannot cleanup document with active connections');
      }

      const removed = this.documentManager.removeDocument(documentId);
      return removed;
    } catch (error) {
      this.logger.error('Failed to cleanup document', error, { documentId });
      throw error;
    }
  }

  async shutdown() {
    try {
      const allConnections = Array.from(this.connectionManager.connections.values());
      allConnections.forEach(connection => {
        if (connection.ws && connection.ws.readyState === 1) {
          try {
            connection.ws.send(JSON.stringify({
              type: 'server-shutdown',
              message: 'Server is shutting down',
              timestamp: new Date().toISOString()
            }));
          } catch (error) {
            this.logger.warn('Failed to notify client of shutdown', error, {
              connectionId: connection.id
            });
          }
        }
      });

      await new Promise(resolve => setTimeout(resolve, 1000));
      this.documentManager.destroy();
    } catch (error) {
      this.logger.error('Error during YJS Service shutdown', error);
      throw error;
    }
  }

  healthCheck() {
    try {
      const stats = this.getStats();

      return {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

export default YjsService;
