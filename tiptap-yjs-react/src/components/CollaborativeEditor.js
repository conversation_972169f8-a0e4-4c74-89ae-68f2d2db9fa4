import React, { useState, useEffect, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import './TiptapEditor.css';

const CLIENT_ID = Math.floor(Math.random() * 100000);

// Global storage for Y.js providers to prevent recreation
const globalProviders = new Map();

const getOrCreateProvider = (serverUrl, documentId) => {
  const key = `${serverUrl}/${documentId}`;

  if (!globalProviders.has(key)) {
    console.log('🔗 [Client', CLIENT_ID, '] Creating new Y.js document and WebSocket provider for', key);
    const doc = new Y.Doc();
    const provider = new WebsocketProvider(serverUrl, documentId, doc);

    globalProviders.set(key, { doc, provider, refCount: 0 });
    console.log('🔗 [Client', CLIENT_ID, '] WebSocketProvider:', provider.url, 'doc:', documentId);
  }

  const entry = globalProviders.get(key);
  entry.refCount++;
  console.log('🔗 [Client', CLIENT_ID, '] Using existing provider, refCount:', entry.refCount);

  return entry;
};

const releaseProvider = (serverUrl, documentId) => {
  const key = `${serverUrl}/${documentId}`;
  const entry = globalProviders.get(key);

  if (entry) {
    entry.refCount--;
    console.log('🧹 [Client', CLIENT_ID, '] Releasing provider, refCount:', entry.refCount);

    if (entry.refCount <= 0) {
      console.log('🧹 [Client', CLIENT_ID, '] Destroying provider for', key);
      entry.provider.destroy();
      globalProviders.delete(key);
    }
  }
};

const CollaborativeEditor = ({ documentId = 'demo-document', serverUrl = 'ws://localhost:1234' }) => {
  const [users, setUsers] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('connecting');
  const [isInitialized, setIsInitialized] = useState(false);

  // Get or create the provider
  const providerEntry = useRef(null);
  const userInfoRef = useRef(null);

  if (!providerEntry.current) {
    providerEntry.current = getOrCreateProvider(serverUrl, documentId);
  }

  const { doc: yjsDoc, provider: yjsProvider } = providerEntry.current;

  // Generate random color for user cursor
  function getRandomColor() {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // Generate user info only once
  if (!userInfoRef.current) {
    userInfoRef.current = {
      name: `User ${Math.floor(Math.random() * 1000)}`,
      color: getRandomColor()
    };
  }

  // Set up Y.js provider event listeners
  useEffect(() => {
    if (isInitialized) return; // Prevent multiple initializations

    console.log('🚀 [Client', CLIENT_ID, '] useEffect - Setting up Y.js collaboration...');

    // Set up connection status tracking
    const statusHandler = (event) => {
      console.log('🌍 [Client', CLIENT_ID, '] WebSocket status:', event.status, '| wsconnected:', yjsProvider.wsconnected, '| readyState:', yjsProvider.ws?.readyState);
      setConnectionStatus(event.status);
    };

    const syncedHandler = (isSynced) => {
      console.log('🤝 [Client', CLIENT_ID, '] [YJS SYNCED]', isSynced);
    };

    // Set up awareness for user cursors and count
    const awarenessHandler = () => {
      const states = Array.from(yjsProvider.awareness.getStates().values());
      const usersWithInfo = states.filter(state => state.user && state.user.name);
      console.log('👥 [Client', CLIENT_ID, '] Awareness check:', states.length, 'total states,', usersWithInfo.length, 'users with info:', usersWithInfo.map(s => s.user?.name).join(', '), '| wsconnected:', yjsProvider.wsconnected, '| readyState:', yjsProvider.ws?.readyState);
      setUsers(usersWithInfo);

      // Update connection status based on WebSocket state
      if (yjsProvider.wsconnected && yjsProvider.ws?.readyState === 1) {
        setConnectionStatus('connected');
      } else if (yjsProvider.ws?.readyState === 0) {
        setConnectionStatus('connecting');
      } else {
        setConnectionStatus('disconnected');
      }
    };

    yjsProvider.on('status', statusHandler);
    yjsProvider.on('synced', syncedHandler);
    yjsProvider.awareness.on('change', awarenessHandler);

    // Add connection event listeners
    yjsProvider.on('connection-open', () => {
      console.log('🟢 [Client', CLIENT_ID, '] WebSocket connection opened');
    });

    yjsProvider.on('connection-close', () => {
      console.log('🔴 [Client', CLIENT_ID, '] WebSocket connection closed');
    });

    yjsProvider.on('connection-error', (error) => {
      console.log('❌ [Client', CLIENT_ID, '] WebSocket connection error:', error);
    });

    // Set local user info
    console.log('👤 [Client', CLIENT_ID, '] Setting local user:', userInfoRef.current.name, userInfoRef.current.color);
    yjsProvider.awareness.setLocalStateField('user', userInfoRef.current);

    // Force awareness update to ensure user shows up in awareness state immediately
    yjsProvider.awareness.setLocalState(yjsProvider.awareness.getLocalState());

    // Periodic connection status check
    const statusInterval = setInterval(() => {
      const states = Array.from(yjsProvider.awareness.getStates().values());
      const usersWithInfo = states.filter(state => state.user && state.user.name);
      console.log('🔍 [Client', CLIENT_ID, '] Status check - wsconnected:', yjsProvider.wsconnected, '| readyState:', yjsProvider.ws?.readyState, '| users:', usersWithInfo.length);

      // Update connection status based on actual WebSocket state
      if (yjsProvider.wsconnected && yjsProvider.ws?.readyState === 1) {
        setConnectionStatus('connected');
      } else if (yjsProvider.ws?.readyState === 0) {
        setConnectionStatus('connecting');
      } else {
        setConnectionStatus('disconnected');
      }

      // Update users count
      setUsers(usersWithInfo);
    }, 2000);

    setIsInitialized(true);

    // Cleanup on unmount
    return () => {
      console.log('🧹 [Client', CLIENT_ID, '] Cleaning up Y.js provider event listeners');
      clearInterval(statusInterval);
      yjsProvider.off('status', statusHandler);
      yjsProvider.off('synced', syncedHandler);
      yjsProvider.awareness.off('change', awarenessHandler);

      // Release the provider reference
      releaseProvider(serverUrl, documentId);
      providerEntry.current = null;
    };
  }, [yjsProvider, isInitialized, serverUrl, documentId]);

  // Create collaborative editor with Y.js
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false, // Y.js handles history
      }),
      Underline,
      Collaboration.configure({
        document: yjsDoc,
      }),
      CollaborationCursor.configure({
        provider: yjsProvider,
        user: userInfoRef.current,
      }),
    ],
    content: '<p>🚀 Start typing to collaborate in real-time!</p>',
    editorProps: {
      attributes: {
        class: 'tiptap-editor-content',
      },
    },
  }, [yjsDoc, yjsProvider]); // Add dependencies to ensure editor updates when provider changes

  // Show loading state while editor is initializing
  if (!editor) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Initializing collaborative editor...</p>
        <small>Setting up Tiptap + Y.js collaboration...</small>
        <div style={{ marginTop: '10px', fontSize: '12px', color: '#999' }}>
          Server: {serverUrl} | Document: {documentId}
        </div>
      </div>
    );
  }

  return (
    <div className="tiptap-editor">
      {/* Header with connection status */}
      <div className="editor-header">
        <h2>Tiptap Editor</h2>
        <div className="connection-info">
          <span className={`status ${connectionStatus}`}>
            {connectionStatus === 'connected' ? '🟢' : connectionStatus === 'connecting' ? '🟡' : '🔴'}
            {connectionStatus}
          </span>
          <span className="document-id">Document: {documentId}</span>
          <span className="users-count">👥 {users.length} user(s) connected</span>
          <span className="collaboration-status">✅ Real-time Collaboration Active</span>
        </div>
      </div>

      {/* Toolbar */}
      <div className="toolbar">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'is-active' : ''}
        >
          Bold
        </button>
        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'is-active' : ''}
        >
          Italic
        </button>
        <button
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={editor.isActive('underline') ? 'is-active' : ''}
        >
          Underline
        </button>
        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'is-active' : ''}
        >
          Strike
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
          className={editor.isActive('heading', { level: 1 }) ? 'is-active' : ''}
        >
          H1
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={editor.isActive('heading', { level: 2 }) ? 'is-active' : ''}
        >
          H2
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          className={editor.isActive('heading', { level: 3 }) ? 'is-active' : ''}
        >
          H3
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'is-active' : ''}
        >
          Bullet List
        </button>
        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'is-active' : ''}
        >
          Numbered List
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'is-active' : ''}
        >
          Quote
        </button>
        <button
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'is-active' : ''}
        >
          Code Block
        </button>
      </div>

      {/* Editor */}
      <div className="editor-container">
        <EditorContent editor={editor} />
      </div>

      {/* Footer */}
      <div className="editor-footer">
        <small>
          Real-time collaborative editing powered by Tiptap + Y.js
          {connectionStatus === 'connected' && (
            <span style={{ color: 'green', marginLeft: '10px' }}>
              ✅ Connected to {serverUrl}
            </span>
          )}
        </small>
      </div>
    </div>
  );
};

export default CollaborativeEditor;
