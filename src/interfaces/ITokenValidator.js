/**
 * Interface for JWT token validation
 * Follows Single Responsibility Principle (SRP)
 */
class ITokenValidator {
  /**
   * Validates JWT token signature and expiration
   * @param {string} token - JWT token to validate
   * @returns {Promise<Object>} Decoded token payload
   */
  async validate(token) {
    throw new Error('Method validate must be implemented');
  }

  /**
   * Extracts token from request headers
   * @param {Object} headers - Request headers
   * @returns {string|null} Extracted token or null
   */
  extractToken(headers) {
    throw new Error('Method extractToken must be implemented');
  }

  /**
   * Checks if token is expired
   * @param {Object} payload - Decoded token payload
   * @returns {boolean} True if token is expired
   */
  isExpired(payload) {
    throw new Error('Method isExpired must be implemented');
  }
}

export default ITokenValidator;
