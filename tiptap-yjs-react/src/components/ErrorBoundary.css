.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin: 20px 0;
}

.error-boundary-content {
  text-align: center;
  max-width: 500px;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.error-boundary h2 {
  color: #721c24;
  margin-bottom: 15px;
  font-size: 24px;
}

.error-boundary p {
  color: #6c757d;
  margin-bottom: 25px;
  font-size: 16px;
  line-height: 1.5;
}

.error-details {
  text-align: left;
  margin: 20px 0;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
}

.error-details summary {
  cursor: pointer;
  font-weight: 500;
  color: #495057;
  margin-bottom: 10px;
}

.error-stack {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #721c24;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #0056b3;
}

.retry-button.secondary {
  background: #6c757d;
}

.retry-button.secondary:hover {
  background: #545b62;
}

@media (max-width: 768px) {
  .error-boundary {
    padding: 20px;
    min-height: 300px;
  }
  
  .error-icon {
    font-size: 48px;
  }
  
  .error-boundary h2 {
    font-size: 20px;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .retry-button {
    width: 100%;
  }
}
