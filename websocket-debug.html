<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Debug Tool</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        .container {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #3e3e42;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #0e5a0e; color: #4dff4d; }
        .disconnected { background-color: #5a0e0e; color: #ff4d4d; }
        .connecting { background-color: #5a5a0e; color: #ffff4d; }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .controls input, .controls button {
            padding: 8px 12px;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            background: #1e1e1e;
            color: #d4d4d4;
        }
        .controls button {
            background: #0e639c;
            color: white;
            cursor: pointer;
        }
        .controls button:hover {
            background: #1177bb;
        }
        .controls button:disabled {
            background: #3e3e42;
            cursor: not-allowed;
        }
        
        .log {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-timestamp {
            color: #808080;
            margin-right: 10px;
        }
        .log-type {
            font-weight: bold;
            margin-right: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
        .log-info { background: #0e639c; color: white; }
        .log-success { background: #0e5a0e; color: #4dff4d; }
        .log-warning { background: #5a5a0e; color: #ffff4d; }
        .log-error { background: #5a0e0e; color: #ff4d4d; }
        .log-debug { background: #5a0e5a; color: #ff4dff; }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-item {
            background: #1e1e1e;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #3e3e42;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4dff4d;
        }
        .stat-label {
            color: #808080;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 WebSocket Debug Tool</h1>
    
    <div class="container">
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="controls">
            <input type="text" id="serverUrl" value="ws://localhost:3000" placeholder="WebSocket Server URL">
            <input type="text" id="documentId" value="debug-doc" placeholder="Document ID">
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
            <button onclick="testMessage()">Send Test Message</button>
        </div>
    </div>
    
    <div class="container">
        <h3>📊 Connection Stats</h3>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="readyState">-</div>
                <div class="stat-label">Ready State</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="messageCount">0</div>
                <div class="stat-label">Messages Received</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="errorCount">0</div>
                <div class="stat-label">Errors</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="connectionTime">-</div>
                <div class="stat-label">Connection Time</div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h3>📝 WebSocket Event Log</h3>
        <div id="log" class="log"></div>
    </div>

    <!-- Load Y.js and y-websocket -->
    <script src="https://unpkg.com/yjs@13.6.27/dist/yjs.js"></script>
    <script src="https://unpkg.com/y-websocket@1.5.0/dist/y-websocket.js"></script>
    
    <script>
        let ws = null;
        let provider = null;
        let doc = null;
        let messageCount = 0;
        let errorCount = 0;
        let connectionStartTime = null;
        
        const readyStates = {
            0: 'CONNECTING',
            1: 'OPEN',
            2: 'CLOSING',
            3: 'CLOSED'
        };
        
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-timestamp">${timestamp}</span>
                <span class="log-type log-${type}">${type.toUpperCase()}</span>
                ${message}
            `;
            logEl.appendChild(entry);
            logEl.scrollTop = logEl.scrollHeight;
            
            // Also log to console for browser DevTools
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }
        
        function updateStats() {
            document.getElementById('readyState').textContent = ws ? readyStates[ws.readyState] : '-';
            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('errorCount').textContent = errorCount;
            
            if (connectionStartTime && ws && ws.readyState === 1) {
                const duration = Math.round((Date.now() - connectionStartTime) / 1000);
                document.getElementById('connectionTime').textContent = `${duration}s`;
            } else {
                document.getElementById('connectionTime').textContent = '-';
            }
        }
        
        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            const documentId = document.getElementById('documentId').value;
            
            if (!serverUrl || !documentId) {
                log('Please enter server URL and document ID', 'error');
                return;
            }
            
            log(`🔌 Connecting to ${serverUrl}/${documentId}`, 'info');
            updateStatus('Connecting...', 'connecting');
            connectionStartTime = Date.now();
            
            try {
                // Create Y.js document
                doc = new Y.Doc();
                
                // Create WebSocket provider
                provider = new WebsocketProvider(serverUrl, documentId, doc);
                ws = provider.ws;
                
                log(`📡 WebSocket URL: ${ws.url}`, 'debug');
                log(`📡 WebSocket protocol: ${ws.protocol}`, 'debug');
                
                // Set up event listeners
                setupEventListeners();
                
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                
            } catch (error) {
                log(`❌ Connection failed: ${error.message}`, 'error');
                errorCount++;
                updateStatus('Connection Failed', 'disconnected');
                updateStats();
            }
        }
        
        function setupEventListeners() {
            // Y.js provider events
            provider.on('status', (event) => {
                log(`📡 Provider status: ${event.status}`, 'info');
                if (event.status === 'connected') {
                    updateStatus('Connected', 'connected');
                } else if (event.status === 'disconnected') {
                    updateStatus('Disconnected', 'disconnected');
                }
                updateStats();
            });
            
            // Raw WebSocket events
            ws.addEventListener('open', (event) => {
                log('🟢 WebSocket OPEN event', 'success');
                log(`🔗 Connection established to: ${ws.url}`, 'success');
                updateStats();
            });
            
            ws.addEventListener('message', (event) => {
                messageCount++;
                const dataType = typeof event.data;
                const dataSize = event.data.length || event.data.byteLength || 0;
                log(`📨 WebSocket MESSAGE: ${dataType} (${dataSize} bytes)`, 'info');
                
                // Try to decode Y.js message for debugging
                if (event.data instanceof ArrayBuffer) {
                    const uint8Array = new Uint8Array(event.data);
                    log(`📨 Binary data: [${Array.from(uint8Array.slice(0, 10)).join(', ')}${uint8Array.length > 10 ? '...' : ''}]`, 'debug');
                }
                updateStats();
            });
            
            ws.addEventListener('error', (event) => {
                errorCount++;
                log(`🔴 WebSocket ERROR: ${event.type}`, 'error');
                updateStats();
            });
            
            ws.addEventListener('close', (event) => {
                log(`🔴 WebSocket CLOSE: Code ${event.code}, Reason: ${event.reason}`, 'warning');
                updateStatus('Disconnected', 'disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                updateStats();
            });
        }
        
        function disconnect() {
            if (provider) {
                log('🔌 Disconnecting...', 'info');
                provider.destroy();
                provider = null;
                ws = null;
                doc = null;
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            messageCount = 0;
            errorCount = 0;
            updateStats();
        }
        
        function testMessage() {
            if (!doc) {
                log('❌ Not connected to document', 'error');
                return;
            }
            
            const text = doc.getText('content');
            const testMessage = `Test message at ${new Date().toLocaleTimeString()}`;
            text.insert(0, testMessage + '\n');
            log(`📤 Sent test message: "${testMessage}"`, 'info');
        }
        
        // Update stats every second
        setInterval(updateStats, 1000);
        
        // Initial stats update
        updateStats();
        
        log('🚀 WebSocket Debug Tool loaded', 'success');
        log('💡 Open browser DevTools > Network > WS tab to see WebSocket traffic', 'info');
    </script>
</body>
</html>
