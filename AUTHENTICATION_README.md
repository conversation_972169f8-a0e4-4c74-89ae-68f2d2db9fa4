# Y.js WebSocket Server Authentication System

## Overview

This authentication system provides secure, scalable JWT-based authorization for Y.js WebSocket connections with multi-tenant support, Redis caching, and real-time permission updates.

## Features

- ✅ **JWT Token Validation** - Secure token-based authentication
- ✅ **Multi-tenant Isolation** - Complete tenant separation
- ✅ **Redis Permission Caching** - High-performance permission lookup
- ✅ **Real-time Permission Updates** - Instant permission revocation via Redis pub/sub
- ✅ **Feature Toggle** - Easy enable/disable via environment variable
- ✅ **SOLID Principles** - Clean, maintainable, extensible architecture
- ✅ **Fallback API Support** - Django API integration for permission validation
- ✅ **Comprehensive Logging** - Detailed audit trail

## Configuration

### Environment Variables

```bash
# Authentication Configuration
AUTH_ENABLED=true                                    # Enable/disable authentication
JWT_SECRET=your-django-secret-key-here              # JWT signing secret
JWT_ALGORITHM=HS256                                 # JWT algorithm
JWT_ISSUER=django-app                               # JWT issuer
AUTH_CACHE_TTL=300                                  # Cache TTL in seconds (5 minutes)
AUTH_FALLBACK_API_URL=http://localhost:8000/api/auth/validate  # Django fallback API

# Redis Configuration (for auth caching)
REDIS_URL=redis://localhost:6379                   # Redis connection URL
REDIS_AUTH_DB=1                                     # Redis database for auth cache
```

### Quick Setup

1. **Enable Authentication:**
   ```bash
   echo "AUTH_ENABLED=true" >> .env
   echo "JWT_SECRET=your-actual-secret-key" >> .env
   ```

2. **Disable Authentication (Development):**
   ```bash
   echo "AUTH_ENABLED=false" >> .env
   ```

## Architecture

### Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AuthFactory   │───▶│   AuthService    │───▶│ TokenValidator  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ AuthMiddleware  │    │PermissionCache   │    │ Redis Pub/Sub   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### SOLID Principles Implementation

1. **Single Responsibility Principle (SRP)**
   - `JwtTokenValidator`: Only handles JWT validation
   - `RedisPermissionCache`: Only handles permission caching
   - `AuthMiddleware`: Only handles WebSocket authentication

2. **Open/Closed Principle (OCP)**
   - Interfaces allow extending without modifying existing code
   - New authentication methods can be added via interfaces

3. **Liskov Substitution Principle (LSP)**
   - All implementations can be substituted for their interfaces
   - Mock implementations for testing

4. **Interface Segregation Principle (ISP)**
   - Separate interfaces for different concerns
   - `IAuthService`, `ITokenValidator`, `IPermissionCache`

5. **Dependency Inversion Principle (DIP)**
   - High-level modules depend on abstractions
   - Dependencies injected via constructor

## Usage

### WebSocket Connection with Authentication

#### Method 1: Authorization Header
```javascript
const provider = new WebsocketProvider(
  'wss://testbank.los.com/realtime/doc-456',
  'doc-456',
  ydoc,
  {
    headers: {
      'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
    }
  }
);
```

#### Method 2: Query Parameter
```javascript
const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...';
const provider = new WebsocketProvider(
  `wss://testbank.los.com/realtime/doc-456?token=${token}`,
  'doc-456',
  ydoc
);
```

#### Method 3: Custom Header
```javascript
const provider = new WebsocketProvider(
  'wss://testbank.los.com/realtime/doc-456',
  'doc-456',
  ydoc,
  {
    headers: {
      'X-Auth-Token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
    }
  }
);
```

### JWT Token Structure

```json
{
  "user_id": 123,
  "tenant_id": "testbank",
  "document_id": "doc-456",
  "permissions": ["read", "write"],
  "user_name": "John Doe",
  "exp": **********,
  "iat": **********,
  "iss": "django-app"
}
```

### Permission Levels

- **`read`**: View document content, see other users' cursors
- **`write`**: Edit document content, full collaboration features
- **`admin`**: All permissions plus ability to manage document access

## API Endpoints

### Health Check
```bash
GET /health
```
Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "uptime": 3600,
  "websocket": "active",
  "auth": {
    "authEnabled": true
  }
}
```

### Statistics
```bash
GET /api/stats
```
Response:
```json
{
  "yjs": {
    "totalConnections": 25,
    "documentsWithConnections": 5
  },
  "auth": {
    "enabled": true,
    "fallbackEnabled": true,
    "cacheEnabled": true,
    "cache": {
      "enabled": true,
      "connected": true,
      "keyCount": 150
    }
  },
  "connections": {
    "total": 25,
    "authenticated": 23
  }
}
```

### Auth Health Check
```bash
GET /api/auth/health
```
Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "components": {
    "authService": {
      "status": "healthy",
      "enabled": true
    },
    "permissionCache": {
      "status": "healthy",
      "enabled": true,
      "connected": true
    },
    "tokenValidator": {
      "status": "healthy"
    }
  }
}
```

## Real-time Permission Updates

### Publishing Permission Changes (Django)

```python
import redis
import json

def revoke_user_permissions(tenant_id, user_id, document_id):
    r = redis.Redis(host='localhost', port=6379, db=1)
    
    channel = f"permissions:{tenant_id}:{document_id}"
    message = {
        "user_id": user_id,
        "action": "revoke",
        "permissions": [],
        "timestamp": datetime.utcnow().isoformat()
    }
    
    r.publish(channel, json.dumps(message))
```

### Update User Permissions

```python
def update_user_permissions(tenant_id, user_id, document_id, new_permissions):
    r = redis.Redis(host='localhost', port=6379, db=1)
    
    channel = f"permissions:{tenant_id}:{document_id}"
    message = {
        "user_id": user_id,
        "action": "update",
        "permissions": new_permissions,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    r.publish(channel, json.dumps(message))
```

## Multi-tenant Security

### Document ID Format
```
{tenant_id}:{document_id}
```

Examples:
- `testbank:doc-456` - Document 456 in testbank tenant
- `acme:report-123` - Report 123 in acme tenant

### Tenant Isolation
- JWT tokens contain `tenant_id`
- Document access validated against token tenant
- Redis cache keys namespaced by tenant
- Cross-tenant access automatically blocked

## Error Handling

### Authentication Errors

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `MISSING_TOKEN` | 401 | No authentication token provided |
| `ACCESS_DENIED` | 403 | Insufficient permissions |
| `AUTH_ERROR` | 401 | Token validation failed |

### WebSocket Close Codes

| Code | Reason |
|------|--------|
| 1008 | Permissions revoked |
| 1008 | Admin disconnect |
| 1011 | Server error during setup |
| 1001 | Server shutting down |

## Development

### Running with Authentication Disabled
```bash
AUTH_ENABLED=false npm start
```

### Running with Authentication Enabled
```bash
AUTH_ENABLED=true \
JWT_SECRET=your-secret \
REDIS_URL=redis://localhost:6379 \
npm start
```

### Testing Authentication
```bash
# Test with valid token
curl -H "Authorization: Bearer valid-jwt-token" \
     -H "Upgrade: websocket" \
     -H "Connection: Upgrade" \
     ws://localhost:1234/test-document

# Test without token (should fail if auth enabled)
curl -H "Upgrade: websocket" \
     -H "Connection: Upgrade" \
     ws://localhost:1234/test-document
```

## Monitoring

### Logs
- Authentication attempts (success/failure)
- Permission cache hits/misses
- Real-time permission updates
- Connection lifecycle events

### Metrics
- Active authenticated connections
- Cache hit ratio
- Authentication failure rate
- Permission update frequency

## Security Considerations

1. **JWT Secret**: Use a strong, unique secret key
2. **Token Expiration**: Recommended 30 minutes with refresh mechanism
3. **HTTPS/WSS**: Always use encrypted connections in production
4. **Rate Limiting**: Implement connection rate limiting
5. **Origin Validation**: Validate WebSocket origins
6. **Audit Logging**: Log all authentication events

## Troubleshooting

### Common Issues

1. **Authentication always fails**
   - Check `JWT_SECRET` matches Django secret
   - Verify token format and claims
   - Check Redis connectivity

2. **Permissions not updating in real-time**
   - Verify Redis pub/sub configuration
   - Check Redis database number
   - Ensure Django publishes to correct channel

3. **Cache not working**
   - Check `REDIS_URL` configuration
   - Verify Redis server is running
   - Check Redis database permissions

### Debug Mode
```bash
LOG_LEVEL=debug AUTH_ENABLED=true npm start
```

This will provide detailed authentication logs for troubleshooting.
