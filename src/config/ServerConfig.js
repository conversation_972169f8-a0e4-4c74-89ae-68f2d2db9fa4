class ServerConfig {
  constructor() {
    this.config = {
      port: process.env.PORT || 1234,
      host: process.env.HOST || '0.0.0.0',
      cors: {
        origin: process.env.CORS_ORIGIN || '*',
        methods: (process.env.CORS_METHODS || 'GET,POST').split(','),
        credentials: process.env.CORS_CREDENTIALS === 'true' || true
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined'
      },
      yjs: {
        persistence: process.env.YJS_PERSISTENCE || false,
        gcEnabled: process.env.YJS_GC_ENABLED !== 'false',
        cleanupInterval: parseInt(process.env.YJS_CLEANUP_INTERVAL) || 300000, // 5 minutes
        maxIdleTime: parseInt(process.env.YJS_MAX_IDLE_TIME) || 1800000 // 30 minutes
      },
      websocket: {
        pingTimeout: parseInt(process.env.WS_PING_TIMEOUT) || 30000,
        pingInterval: parseInt(process.env.WS_PING_INTERVAL) || 25000,
        maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS) || 1000
      },
      auth: {
        enabled: process.env.AUTH_ENABLED === 'true',
        jwt: {
          secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
          algorithm: process.env.JWT_ALGORITHM || 'HS256',
          issuer: process.env.JWT_ISSUER || 'yjs-server',
          audience: process.env.JWT_AUDIENCE || undefined, // Optional audience validation
          maxAge: process.env.JWT_MAX_AGE || '24h',        // Maximum token age
          maxTokenAge: parseInt(process.env.JWT_MAX_TOKEN_AGE) || 86400, // Max age in seconds
          clockTolerance: parseInt(process.env.JWT_CLOCK_TOLERANCE) || 30, // Clock skew tolerance
          tokenSizeLimit: parseInt(process.env.JWT_TOKEN_SIZE_LIMIT) || 8192 // Token size limit
        },
        cache: {
          ttl: parseInt(process.env.AUTH_CACHE_TTL) || 300, // 5 minutes
          enabled: process.env.AUTH_ENABLED === 'true'
        },
        fallback: {
          apiUrl: process.env.AUTH_FALLBACK_API_URL || null,
          enabled: !!process.env.AUTH_FALLBACK_API_URL
        }
      },
      redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        authDb: parseInt(process.env.REDIS_AUTH_DB) || 1,
        retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY) || 100,
        maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES) || 3,
        connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT) || 5000
      },
      haproxy: {
        maxConn: parseInt(process.env.HAPROXY_MAX_CONN) || 4096,
        timeouts: {
          connect: parseInt(process.env.HAPROXY_TIMEOUT_CONNECT) || 5000,
          client: parseInt(process.env.HAPROXY_TIMEOUT_CLIENT) || 50000,
          server: parseInt(process.env.HAPROXY_TIMEOUT_SERVER) || 50000,
          tunnel: parseInt(process.env.HAPROXY_TIMEOUT_TUNNEL) || 3600000,
          httpKeepAlive: parseInt(process.env.HAPROXY_TIMEOUT_HTTP_KEEP_ALIVE) || 10000,
          httpRequest: parseInt(process.env.HAPROXY_TIMEOUT_HTTP_REQUEST) || 10000
        },
        retries: parseInt(process.env.HAPROXY_RETRIES) || 3
      },
      error: {
        stackTraceEnabled: process.env.ERROR_STACK_TRACE_ENABLED === 'true' || true,
        detailedMessages: process.env.ERROR_DETAILED_MESSAGES === 'true' || true
      },
      connection: {
        idLength: parseInt(process.env.CONNECTION_ID_LENGTH) || 11,
        cleanupInterval: parseInt(process.env.CONNECTION_CLEANUP_INTERVAL) || 60000,
        maxIdleTime: parseInt(process.env.CONNECTION_MAX_IDLE_TIME) || 300000
      },
      security: {
        rateLimitWindow: parseInt(process.env.SECURITY_RATE_LIMIT_WINDOW) || 60000,
        rateLimitMaxRequests: parseInt(process.env.SECURITY_RATE_LIMIT_MAX_REQUESTS) || 100,
        helmetEnabled: process.env.SECURITY_HELMET_ENABLED === 'true' || true
      },
      health: {
        checkInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000,
        checkTimeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000
      },
      static: {
        path: process.env.STATIC_FILES_PATH || 'public',
        maxAge: parseInt(process.env.STATIC_FILES_MAX_AGE) || 86400
      }
    };
  }

  get(key) {
    return key ? this.config[key] : this.config;
  }

  set(key, value) {
    this.config[key] = value;
  }

  validate() {
    const requiredFields = ['port', 'host'];
    const missing = requiredFields.filter(field => !this.config[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required configuration fields: ${missing.join(', ')}`);
    }
    
    return true;
  }
}

export default ServerConfig;
