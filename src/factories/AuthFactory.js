import AuthService from '../services/AuthService.js';
import JwtTokenValidator from '../services/JwtTokenValidator.js';
import RedisPermissionCache from '../services/RedisPermissionCache.js';
import AuthMiddleware from '../middleware/AuthMiddleware.js';

/**
 * Factory for creating authentication components
 * Follows Dependency Injection and Factory patterns
 */
class AuthFactory {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    this.authService = null;
    this.authMiddleware = null;
    this.permissionCache = null;
    this.tokenValidator = null;
  }

  /**
   * Creates and initializes all authentication components
   * @returns {Promise<Object>} Authentication components
   */
  async createAuthComponents() {
    try {
      this.logger.info('Initializing authentication components', {
        authEnabled: this.config.get('auth').enabled
      });

      // Step 1: Create token validator
      this.tokenValidator = new JwtTokenValidator(this.config, this.logger);

      // Step 2: Create and initialize permission cache
      this.permissionCache = new RedisPermissionCache(this.config, this.logger);
      await this.permissionCache.initialize();

      // Step 3: Create auth service with dependencies
      this.authService = new AuthService(
        this.config,
        this.logger,
        this.tokenValidator,
        this.permissionCache
      );

      // Step 4: Create auth middleware
      this.authMiddleware = new AuthMiddleware(this.authService, this.logger);

      // Step 5: Set up permission update subscriptions
      if (this.permissionCache.isEnabled()) {
        await this.setupPermissionSubscriptions();
      }

      this.logger.info('Authentication components initialized successfully');

      return {
        authService: this.authService,
        authMiddleware: this.authMiddleware,
        permissionCache: this.permissionCache,
        tokenValidator: this.tokenValidator
      };

    } catch (error) {
      this.logger.error('Failed to initialize authentication components', error);
      throw error;
    }
  }

  /**
   * Sets up Redis subscriptions for permission updates
   * @returns {Promise<void>}
   */
  async setupPermissionSubscriptions() {
    try {
      await this.permissionCache.subscribeToUpdates((channel, updateData) => {
        // This callback will be called when permission updates are received
        // The actual disconnection logic will be handled by the WebSocket server
        this.logger.debug('Permission update received', {
          channel,
          updateData
        });

        // Emit event that can be handled by the WebSocket server
        if (this.permissionUpdateHandler) {
          this.permissionUpdateHandler(channel, updateData);
        }
      });

      this.logger.info('Permission update subscriptions established');
    } catch (error) {
      this.logger.error('Failed to set up permission subscriptions', error);
    }
  }

  /**
   * Sets permission update handler
   * @param {Function} handler - Permission update handler function
   */
  setPermissionUpdateHandler(handler) {
    this.permissionUpdateHandler = handler;
  }

  /**
   * Creates a no-op auth service when authentication is disabled
   * @returns {Object} No-op authentication components
   */
  createNoOpAuthComponents() {
    this.logger.info('Creating no-op authentication components (auth disabled)');

    const noOpAuthService = {
      isEnabled: () => false,
      validateToken: async () => ({ isAuthenticated: false, isGuest: true }),
      validateDocumentAccess: async () => true,
      invalidateCache: async () => {},
      getStats: async () => ({ enabled: false })
    };

    const noOpAuthMiddleware = {
      authenticate: async () => ({
        success: true,
        userContext: { isAuthenticated: false, isGuest: true },
        reason: 'auth_disabled'
      }),
      getStats: () => ({ authEnabled: false })
    };

    return {
      authService: noOpAuthService,
      authMiddleware: noOpAuthMiddleware,
      permissionCache: null,
      tokenValidator: null
    };
  }

  /**
   * Gets authentication statistics from all components
   * @returns {Promise<Object>} Combined statistics
   */
  async getStats() {
    try {
      const stats = {
        factory: {
          initialized: !!this.authService,
          timestamp: new Date().toISOString()
        }
      };

      if (this.authService) {
        stats.authService = await this.authService.getStats();
      }

      if (this.authMiddleware) {
        stats.authMiddleware = this.authMiddleware.getStats();
      }

      if (this.permissionCache) {
        stats.permissionCache = await this.permissionCache.getStats();
      }

      return stats;
    } catch (error) {
      this.logger.error('Failed to get auth factory stats', error);
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Gracefully shuts down authentication components
   * @returns {Promise<void>}
   */
  async shutdown() {
    try {
      this.logger.info('Shutting down authentication components');

      if (this.permissionCache) {
        await this.permissionCache.close();
      }

      this.authService = null;
      this.authMiddleware = null;
      this.permissionCache = null;
      this.tokenValidator = null;

      this.logger.info('Authentication components shut down successfully');
    } catch (error) {
      this.logger.error('Failed to shutdown authentication components', error);
    }
  }

  /**
   * Health check for authentication components
   * @returns {Promise<Object>} Health status
   */
  async healthCheck() {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        components: {}
      };

      // Check auth service
      if (this.authService) {
        health.components.authService = {
          status: 'healthy',
          enabled: this.authService.isEnabled()
        };
      }

      // Check permission cache
      if (this.permissionCache) {
        const cacheStats = await this.permissionCache.getStats();
        health.components.permissionCache = {
          status: cacheStats.connected ? 'healthy' : 'unhealthy',
          enabled: cacheStats.enabled,
          connected: cacheStats.connected
        };

        if (!cacheStats.connected && cacheStats.enabled) {
          health.status = 'degraded';
        }
      }

      // Check token validator
      if (this.tokenValidator) {
        health.components.tokenValidator = {
          status: 'healthy'
        };
      }

      return health;
    } catch (error) {
      this.logger.error('Auth factory health check failed', error);
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Validates factory configuration
   * @returns {Object} Validation result
   */
  validateConfiguration() {
    const authConfig = this.config.get('auth');
    const issues = [];

    if (authConfig.enabled) {
      // Check JWT configuration
      if (!authConfig.jwt.secret || authConfig.jwt.secret === 'default-secret-change-in-production') {
        issues.push('JWT secret is not configured or using default value');
      }

      if (!authConfig.jwt.algorithm) {
        issues.push('JWT algorithm is not configured');
      }

      // Check Redis configuration if caching is enabled
      if (authConfig.cache.enabled) {
        const redisConfig = this.config.get('redis');
        if (!redisConfig.url) {
          issues.push('Redis URL is not configured but caching is enabled');
        }
      }

      // Check fallback API configuration
      if (authConfig.fallback.enabled && !authConfig.fallback.apiUrl) {
        issues.push('Fallback API URL is not configured but fallback is enabled');
      }
    }

    return {
      valid: issues.length === 0,
      issues,
      timestamp: new Date().toISOString()
    };
  }
}

export default AuthFactory;
