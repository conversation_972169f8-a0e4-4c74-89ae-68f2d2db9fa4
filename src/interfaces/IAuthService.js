/**
 * Interface for authentication services
 * Follows Interface Segregation Principle (ISP)
 */
class IAuthService {
  /**
   * Validates a JWT token and returns user context
   * @param {string} token - JWT token to validate
   * @returns {Promise<Object>} User context with permissions
   */
  async validateToken(token) {
    throw new Error('Method validateToken must be implemented');
  }

  /**
   * Checks if authentication is enabled
   * @returns {boolean} True if auth is enabled
   */
  isEnabled() {
    throw new Error('Method isEnabled must be implemented');
  }

  /**
   * Validates user access to a specific document
   * @param {Object} userContext - User context from token validation
   * @param {string} documentId - Document ID to check access for
   * @param {string} action - Action type (read, write, admin)
   * @returns {Promise<boolean>} True if user has access
   */
  async validateDocumentAccess(userContext, documentId, action = 'read') {
    throw new Error('Method validateDocumentAccess must be implemented');
  }

  /**
   * Invalidates cached permissions for a user
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID (optional)
   * @returns {Promise<void>}
   */
  async invalidateCache(userId, documentId = null) {
    throw new Error('Method invalidateCache must be implemented');
  }
}

export default IAuthService;
