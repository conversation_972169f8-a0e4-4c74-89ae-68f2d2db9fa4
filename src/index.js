import 'dotenv/config';
import ServerConfig from './config/ServerConfig.js';
import Logger from './utils/Logger.js';
import WebSocketServer from './server/WebSocketServer.js';
import ConnectionManager from './managers/ConnectionManager.js';
import DocumentManager from './managers/DocumentManager.js';
import WebSocketHandler from './handlers/WebSocketHandler.js';
import YjsService from './services/YjsService.js';

class RealtimeYjsServer {
  constructor() {
    this.config = null;
    this.logger = null;
    this.webSocketServer = null;
    this.connectionManager = null;
    this.documentManager = null;
    this.webSocketHandler = null;
    this.yjsService = null;
    this.isShuttingDown = false;
  }

  async initialize() {
    try {
      this.config = new ServerConfig();
      this.config.validate();

      this.logger = new Logger(this.config.get('logging'));
      this.connectionManager = new ConnectionManager(this.logger);
      this.documentManager = new DocumentManager(this.logger, this.config.get('yjs'));

      this.webSocketHandler = new WebSocketHandler(
        this.connectionManager,
        this.documentManager,
        this.logger
      );

      this.yjsService = new YjsService(
        this.connectionManager,
        this.documentManager,
        this.webSocketHandler,
        this.logger
      );

      this.webSocketServer = new WebSocketServer(this.config, this.logger);
      this.webSocketServer.initialize();
      this.webSocketServer.setYjsService(this.yjsService);
      this.webSocketServer.initializeWebSocket();
      this.yjsService.initialize();
    } catch (error) {
      this.logger.error('Failed to initialize application', error);
      throw error;
    }
  }

  async start() {
    try {
      await this.webSocketServer.start();
      this.setupGracefulShutdown();
    } catch (error) {
      this.logger.error('Failed to start server', error);
      throw error;
    }
  }

  setupGracefulShutdown() {
    const shutdownHandler = async () => {
      if (this.isShuttingDown) {
        process.exit(1);
      }

      this.isShuttingDown = true;

      try {
        if (this.yjsService) {
          await this.yjsService.shutdown();
        }
        if (this.webSocketServer) {
          await this.webSocketServer.stop();
        }
        process.exit(0);
      } catch (error) {
        this.logger.error('Error during graceful shutdown', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdownHandler('SIGTERM'));
    process.on('SIGINT', () => shutdownHandler('SIGINT'));
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught Exception', error);
      shutdownHandler('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled Rejection', reason, { promise });
      shutdownHandler('unhandledRejection');
    });
  }

  getHealthStatus() {
    if (this.yjsService) {
      return this.yjsService.healthCheck();
    }
    return { status: 'unhealthy', message: 'Service not initialized' };
  }
}

async function main() {
  const server = new RealtimeYjsServer();

  try {
    await server.initialize();
    await server.start();
  } catch (error) {
    console.error('Failed to start Realtime YJS Server:', error);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default RealtimeYjsServer;
