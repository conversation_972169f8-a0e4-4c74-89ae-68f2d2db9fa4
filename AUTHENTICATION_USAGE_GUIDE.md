# Authentication Usage Guide

This guide explains how to use the JWT authentication system with the Y.js WebSocket server.

## Quick Start

### 1. **Disable Authentication (Default - Recommended for Testing)**

```bash
# In .env file
AUTH_ENABLED=false
```

With authentication disabled, WebSocket connections work without any tokens. This is the current default setting.

### 2. **Enable Authentication**

```bash
# In .env file
AUTH_ENABLED=true
JWT_SECRET=your-django-secret-key-here
JWT_ISSUER=django-app
JWT_AUDIENCE=yjs-websocket-server
```

## Testing with Authentication

### Option 1: Use the React Demo with Built-in Authentication

1. **Open the React demo**: http://localhost:3000
2. **Check "Enable Authentication (JWT)"** checkbox
3. **Leave token field empty** for auto-generated demo token
4. **Start collaborating** - the demo will automatically generate valid JWT tokens

### Option 2: Generate Custom Demo Tokens

```bash
# Generate a basic demo token
npm run generate-demo-token

# Generate token with custom parameters
npm run generate-demo-token -- --user-id 123 --document-id my-doc --expires 1

# Generate token for specific tenant
npm run generate-demo-token -- --tenant-id testbank --user-name "John Doe"
```

### Option 3: Use Custom JWT Tokens

If you have your own JWT tokens from Django or another system:

1. **Open React demo**: http://localhost:3000
2. **Check "Enable Authentication (JWT)"**
3. **Paste your JWT token** in the text area
4. **Start collaborating**

## WebSocket Connection Methods

### Method 1: Query Parameter (Recommended for WebSocket)
```javascript
const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...';
const provider = new WebsocketProvider(
  `ws://localhost:1234/demo-document?token=${token}`,
  'demo-document',
  ydoc
);
```

### Method 2: Authorization Header
```javascript
const provider = new WebsocketProvider(
  'ws://localhost:1234/demo-document',
  'demo-document',
  ydoc,
  {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  }
);
```

### Method 3: Custom Header
```javascript
const provider = new WebsocketProvider(
  'ws://localhost:1234/demo-document',
  'demo-document',
  ydoc,
  {
    headers: {
      'X-Auth-Token': token
    }
  }
);
```

## JWT Token Structure

Your JWT tokens should include these claims:

```json
{
  "user_id": 123,
  "tenant_id": "testbank",
  "document_id": "doc-456",
  "permissions": ["read", "write"],
  "user_name": "John Doe",
  "exp": **********,
  "iat": **********,
  "iss": "django-app",
  "aud": "yjs-websocket-server"
}
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `AUTH_ENABLED` | Enable/disable authentication | `false` |
| `JWT_SECRET` | Secret key for JWT verification | `your-django-secret-key-here` |
| `JWT_ALGORITHM` | JWT signing algorithm | `HS256` |
| `JWT_ISSUER` | Expected JWT issuer | `django-app` |
| `JWT_AUDIENCE` | Expected JWT audience | `yjs-websocket-server` |
| `JWT_MAX_AGE` | Maximum token age | `24h` |
| `JWT_CLOCK_TOLERANCE` | Clock skew tolerance | `30` seconds |

## Troubleshooting

### Connection Stuck on "Connecting..."

**Cause**: Authentication is enabled but no valid JWT token provided.

**Solutions**:
1. **Disable authentication**: Set `AUTH_ENABLED=false` in `.env`
2. **Provide valid token**: Use the React demo's authentication controls
3. **Generate demo token**: Run `npm run generate-demo-token`

### "Invalid Token" Errors

**Cause**: Token is malformed, expired, or signed with wrong secret.

**Solutions**:
1. **Check JWT secret**: Ensure `JWT_SECRET` matches your Django secret
2. **Verify token expiration**: Generate a new token
3. **Check token format**: Ensure it's a valid JWT with all required claims

### "Access Denied" Errors

**Cause**: Token is valid but user doesn't have permission for the document.

**Solutions**:
1. **Check permissions**: Ensure token includes `["read", "write"]` permissions
2. **Verify document_id**: Ensure token's `document_id` matches the WebSocket URL
3. **Use demo token**: Demo tokens have full permissions for testing

## Production Integration

### Django Integration

1. **Generate JWT tokens** in your Django views:
```python
import jwt
from django.conf import settings

def generate_realtime_token(user, document_id):
    payload = {
        'user_id': user.id,
        'tenant_id': user.tenant.slug,
        'document_id': document_id,
        'permissions': ['read', 'write'],
        'user_name': user.get_full_name(),
        'exp': timezone.now() + timedelta(hours=24),
        'iat': timezone.now(),
        'iss': 'django-app',
        'aud': 'yjs-websocket-server'
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
```

2. **Pass token to frontend**:
```javascript
// In your Vue.js/React component
const token = await fetch('/api/realtime-auth/', {
  method: 'POST',
  body: JSON.stringify({ document_id: documentId })
}).then(r => r.json()).then(data => data.token);

const provider = new WebsocketProvider(
  `wss://yourdomain.com/realtime/${documentId}?token=${token}`,
  documentId,
  ydoc
);
```

## Security Best Practices

1. **Use HTTPS/WSS** in production
2. **Set short token expiration** (1-24 hours)
3. **Validate document permissions** on every connection
4. **Use strong JWT secrets** (32+ characters)
5. **Implement token refresh** for long-lived connections
6. **Log authentication events** for security monitoring

## Commands Reference

```bash
# Start with authentication disabled (default)
make docker-up

# Generate demo token
npm run generate-demo-token

# Generate custom token
npm run generate-demo-token -- --user-id 123 --document-id my-doc

# Check server status
curl http://localhost:1234/health

# Restart servers after config changes
docker restart yjs-server-1 yjs-server-2
```
