import jwt from 'jsonwebtoken';
import ITokenValidator from '../interfaces/ITokenValidator.js';

/**
 * JWT Token Validator implementation
 * Follows Single Responsibility Principle (SRP)
 */
class JwtTokenValidator extends ITokenValidator {
  constructor(config, logger) {
    super();
    this.config = config;
    this.logger = logger;
    this.jwtConfig = config.get('auth').jwt;
  }

  /**
   * Validates JWT token signature and expiration
   * @param {string} token - JWT token to validate
   * @returns {Promise<Object>} Decoded token payload
   */
  async validate(token) {
    try {
      // Security: Basic input validation
      if (!token || typeof token !== 'string') {
        throw new Error('Invalid token format');
      }

      // Security: Check token length to prevent DoS attacks
      if (token.length > 8192) { // 8KB limit
        throw new Error('Token too large');
      }

      // Security: Basic format validation (should have 3 parts)
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        throw new Error('Invalid token structure');
      }

      // Enhanced JWT verification with additional security options
      const payload = jwt.verify(token, this.jwtConfig.secret, {
        algorithms: [this.jwtConfig.algorithm], // Prevent algorithm confusion attacks
        issuer: this.jwtConfig.issuer,          // Validate issuer
        audience: this.jwtConfig.audience || undefined, // Validate audience if configured
        clockTolerance: 30,                     // 30 seconds clock skew tolerance
        maxAge: this.jwtConfig.maxAge || '24h', // Maximum token age
        ignoreExpiration: false,                // Always check expiration
        ignoreNotBefore: false                  // Always check nbf claim
      });

      // Additional security validations
      this.validateTokenClaims(payload);

      this.logger.debug('JWT token validated successfully', {
        userId: payload.user_id,
        tenantId: payload.tenant_id,
        documentId: payload.document_id,
        exp: payload.exp,
        iat: payload.iat
      });

      return payload;
    } catch (error) {
      this.logger.warn('JWT token validation failed', {
        error: error.message,
        errorType: error.name,
        tokenLength: token ? token.length : 0,
        tokenPreview: token ? `${token.substring(0, 20)}...` : 'null'
      });
      throw new Error(`Token validation failed: ${error.message}`);
    }
  }

  /**
   * Extracts token from request headers
   * @param {Object} headers - Request headers
   * @returns {string|null} Extracted token or null
   */
  extractToken(headers) {
    try {
      // Check Authorization header (Bearer token)
      const authHeader = headers.authorization || headers.Authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
      }

      // Check custom header
      const customHeader = headers['x-auth-token'] || headers['X-Auth-Token'];
      if (customHeader) {
        return customHeader;
      }

      // Check query parameter (for WebSocket connections)
      const url = headers.url || '';
      const urlParams = new URLSearchParams(url.split('?')[1] || '');
      const tokenFromQuery = urlParams.get('token');
      if (tokenFromQuery) {
        return tokenFromQuery;
      }

      return null;
    } catch (error) {
      this.logger.warn('Failed to extract token from headers', {
        error: error.message,
        headers: Object.keys(headers)
      });
      return null;
    }
  }

  /**
   * Validates token claims for security
   * @param {Object} payload - Decoded token payload
   * @throws {Error} If claims are invalid
   */
  validateTokenClaims(payload) {
    // Required claims validation
    const requiredClaims = ['user_id', 'tenant_id', 'exp', 'iat'];
    for (const claim of requiredClaims) {
      if (!payload[claim]) {
        throw new Error(`Missing required claim: ${claim}`);
      }
    }

    // Security: Validate user_id format
    if (typeof payload.user_id !== 'number' && typeof payload.user_id !== 'string') {
      throw new Error('Invalid user_id format');
    }

    // Security: Validate tenant_id format
    if (typeof payload.tenant_id !== 'string' || payload.tenant_id.length === 0) {
      throw new Error('Invalid tenant_id format');
    }

    // Security: Check for suspicious tenant_id patterns
    if (payload.tenant_id.includes('..') || payload.tenant_id.includes('/')) {
      throw new Error('Invalid tenant_id contains suspicious characters');
    }

    // Security: Validate timestamp claims
    if (typeof payload.exp !== 'number' || typeof payload.iat !== 'number') {
      throw new Error('Invalid timestamp claims');
    }

    // Security: Check if token was issued in the future (clock skew attack)
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.iat > currentTime + 60) { // 1 minute tolerance
      throw new Error('Token issued in the future');
    }

    // Security: Check token age (prevent replay attacks with old tokens)
    const tokenAge = currentTime - payload.iat;
    const maxTokenAge = this.jwtConfig.maxTokenAge || 86400; // 24 hours default
    if (tokenAge > maxTokenAge) {
      throw new Error('Token too old');
    }

    // Security: Validate permissions if present
    if (payload.permissions && !Array.isArray(payload.permissions)) {
      throw new Error('Invalid permissions format');
    }

    // Security: Validate document_id if present
    if (payload.document_id && typeof payload.document_id !== 'string') {
      throw new Error('Invalid document_id format');
    }
  }

  /**
   * Checks if token is expired
   * @param {Object} payload - JWT payload
   * @returns {boolean} True if token is expired
   */
  isExpired(payload) {
    if (!payload.exp) {
      return false; // No expiration claim
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  }

  /**
   * Validates token structure and required claims
   * @param {Object} payload - Decoded token payload
   * @returns {boolean} True if token structure is valid
   */
  validateTokenStructure(payload) {
    const requiredClaims = ['user_id', 'tenant_id', 'document_id', 'permissions'];
    
    for (const claim of requiredClaims) {
      if (!payload.hasOwnProperty(claim)) {
        this.logger.warn('Missing required claim in JWT token', {
          missingClaim: claim,
          availableClaims: Object.keys(payload)
        });
        return false;
      }
    }

    // Validate permissions array
    if (!Array.isArray(payload.permissions)) {
      this.logger.warn('Invalid permissions format in JWT token', {
        permissions: payload.permissions,
        type: typeof payload.permissions
      });
      return false;
    }

    return true;
  }

  /**
   * Gets token expiration time
   * @param {Object} payload - Decoded token payload
   * @returns {Date|null} Expiration date or null
   */
  getExpirationDate(payload) {
    if (!payload.exp) {
      return null;
    }
    return new Date(payload.exp * 1000);
  }

  /**
   * Gets time until token expires
   * @param {Object} payload - Decoded token payload
   * @returns {number} Seconds until expiration, -1 if expired
   */
  getTimeUntilExpiration(payload) {
    if (!payload.exp) {
      return Infinity;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp - currentTime;
  }
}

export default JwtTokenValidator;
