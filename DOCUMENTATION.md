# Y.js WebSocket Server Documentation

## Overview

This documentation provides a comprehensive guide to the `minimal-server.js` file, which implements a real-time collaborative WebSocket server using Y.js (Yjs) for operational transformation and conflict-free replicated data types (CRDTs).

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Dependencies](#dependencies)
3. [Server Components](#server-components)
4. [Function Documentation](#function-documentation)
5. [Event Handlers](#event-handlers)
6. [Configuration](#configuration)
7. [Usage Examples](#usage-examples)

## Architecture Overview

The server implements a WebSocket-based real-time collaboration system using:
- **Express.js** for HTTP server and static file serving
- **WebSocket (ws)** for real-time bidirectional communication
- **Y.js WebSocket utilities** for handling collaborative document synchronization

```
Client Browser ←→ WebSocket Connection ←→ Y.js Server ←→ Shared Document State
```

## Dependencies

### Required Packages

```javascript
const http = require('http');           // Node.js HTTP server
const WebSocket = require('ws');        // WebSocket implementation
const express = require('express');     // Web framework
const { setupWSConnection } = require('@y/websocket-server/utils'); // Y.js utilities
```

- **http**: Node.js built-in module for creating HTTP servers
- **ws**: Third-party WebSocket library for Node.js
- **express**: Web application framework for serving static files
- **@y/websocket-server**: Y.js server utilities for handling collaborative document operations

## Server Components

### 1. Express Application Setup

```javascript
const app = express();
const server = http.createServer(app);
```

**Purpose**: Creates an Express application instance and wraps it with an HTTP server.

**Details**:
- `app`: Express application instance for handling HTTP requests
- `server`: HTTP server that can handle both HTTP requests and WebSocket upgrades

### 2. Static File Serving

```javascript
app.use(express.static('.'));
```

**Purpose**: Serves static files from the current directory.

**Details**:
- Serves HTML, CSS, JavaScript, and other static assets
- Uses current directory (`.`) as the root for static files
- Enables clients to access the collaborative application interface

### 3. WebSocket Server Creation

```javascript
const wss = new WebSocket.Server({ noServer: true });
```

**Purpose**: Creates a WebSocket server that doesn't automatically bind to an HTTP server.

**Configuration**:
- `noServer: true`: Prevents automatic binding, allowing manual upgrade handling
- Enables custom upgrade logic and better control over WebSocket connections

## Function Documentation

### setupWSConnection(ws, req)

**Source**: `@y/websocket-server/utils`

**Purpose**: Establishes and configures a Y.js collaborative session for a WebSocket connection.

**Parameters**:
- `ws` (WebSocket): The WebSocket connection instance
- `req` (IncomingMessage): The HTTP request object containing connection metadata

**Functionality**:
1. **Document Management**: Creates or retrieves shared Y.js documents based on request URL
2. **Synchronization**: Handles initial document state synchronization with new clients
3. **Operation Broadcasting**: Manages real-time propagation of document changes
4. **Conflict Resolution**: Applies Y.js CRDT algorithms for conflict-free merging
5. **Connection Lifecycle**: Manages client connection and disconnection events

**Internal Operations**:
- Parses document ID from request URL
- Initializes Y.js document instance
- Sets up awareness protocols for cursor/selection sharing
- Establishes message handlers for collaborative operations

## Event Handlers

### 1. WebSocket Connection Handler

```javascript
wss.on('connection', (ws, req) => {
  console.log('New WebSocket connection:', req.url);
  setupWSConnection(ws, req);
});
```

**Trigger**: When a new WebSocket connection is established

**Parameters**:
- `ws`: The WebSocket connection instance
- `req`: HTTP request object with connection details

**Process Flow**:
1. Logs the new connection with request URL
2. Delegates connection setup to Y.js utilities
3. Initializes collaborative document session

**Logging**: Outputs connection URL for debugging and monitoring

### 2. HTTP Upgrade Handler

```javascript
server.on('upgrade', (request, socket, head) => {
  console.log('WebSocket upgrade request:', request.url);
  wss.handleUpgrade(request, socket, head, (ws) => {
    console.log('WebSocket upgrade successful');
    wss.emit('connection', ws, request);
  });
});
```

**Trigger**: When an HTTP connection requests upgrade to WebSocket protocol

**Parameters**:
- `request`: HTTP request object
- `socket`: TCP socket for the connection
- `head`: First packet of upgraded stream

**Process Flow**:
1. **Upgrade Request**: Logs incoming WebSocket upgrade request
2. **Protocol Negotiation**: Handles WebSocket handshake protocol
3. **Connection Establishment**: Creates WebSocket instance upon successful upgrade
4. **Event Emission**: Triggers 'connection' event with new WebSocket

**Error Handling**: Built into `wss.handleUpgrade()` method

### 3. Server Startup

```javascript
const PORT = 3001;
server.listen(PORT, () => {
  console.log(`Minimal Y.js WebSocket server running on port ${PORT}`);
});
```

**Purpose**: Starts the HTTP server and begins listening for connections

**Configuration**:
- **Port**: 3001 (configurable)
- **Host**: Defaults to all available interfaces (0.0.0.0)

**Callback**: Executes when server successfully starts listening

## Configuration

### Port Configuration

```javascript
const PORT = 3001;
```

**Default**: 3001
**Environment Variable**: Can be modified to use `process.env.PORT`
**Considerations**: Ensure port is available and not blocked by firewall

### Static File Directory

```javascript
app.use(express.static('.'));
```

**Default**: Current directory (`.`)
**Customization**: Can be changed to specific directory (e.g., `'./public'`)
**Security**: Consider restricting to specific directories in production

## Usage Examples

### Basic Client Connection

```javascript
// Client-side JavaScript
const websocket = new WebSocket('ws://localhost:3001');
const ydoc = new Y.Doc();
const provider = new WebsocketProvider('ws://localhost:3001', 'my-document', ydoc);
```

### Document Collaboration

```javascript
// Shared text editing
const ytext = ydoc.getText('content');
ytext.insert(0, 'Hello, collaborative world!');
```

### Multiple Document Rooms

```javascript
// Different documents by URL path
const doc1Provider = new WebsocketProvider('ws://localhost:3001', 'document-1', ydoc1);
const doc2Provider = new WebsocketProvider('ws://localhost:3001', 'document-2', ydoc2);
```

## Security Considerations

1. **Origin Validation**: Consider implementing origin checks for WebSocket connections
2. **Authentication**: Add authentication middleware for production use
3. **Rate Limiting**: Implement connection and message rate limiting
4. **Input Validation**: Validate document IDs and operation data

## Performance Notes

1. **Memory Management**: Y.js automatically manages document state in memory
2. **Persistence**: Consider adding database persistence for document storage
3. **Scaling**: For high load, consider clustering or external message brokers
4. **Monitoring**: Add metrics collection for connection counts and document operations

## Troubleshooting

### Common Issues

1. **Port Already in Use**: Change PORT value or kill existing process
2. **WebSocket Connection Failed**: Check firewall settings and network connectivity
3. **Static Files Not Loading**: Verify file paths and permissions
4. **Y.js Sync Issues**: Check client-side Y.js version compatibility

### Debug Logging

The server includes basic console logging for:
- New WebSocket connections
- Upgrade requests
- Server startup confirmation

For enhanced debugging, consider adding more detailed logging throughout the Y.js operations.
