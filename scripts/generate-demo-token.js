#!/usr/bin/env node

/**
 * Demo JWT Token Generator
 * Generates valid JWT tokens for testing the Y.js WebSocket server with authentication enabled
 */

import crypto from 'crypto';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config();

// JWT Helper functions
function base64UrlEncode(str) {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

function createJWT(payload, secret, algorithm = 'HS256') {
  const header = {
    alg: algorithm,
    typ: 'JWT'
  };

  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// Generate demo token
function generateDemoToken(options = {}) {
  const {
    userId = Math.floor(Math.random() * 1000) + 1,
    tenantId = 'demo-tenant',
    documentId = 'demo-document',
    userName = `Demo User ${userId}`,
    permissions = ['read', 'write'],
    expiresInHours = 24
  } = options;

  const now = Math.floor(Date.now() / 1000);
  const payload = {
    user_id: userId,
    tenant_id: tenantId,
    document_id: documentId,
    permissions: permissions,
    user_name: userName,
    exp: now + (expiresInHours * 60 * 60),
    iat: now,
    iss: process.env.JWT_ISSUER || 'django-app',
    aud: process.env.JWT_AUDIENCE || 'yjs-websocket-server'
  };

  const secret = process.env.JWT_SECRET || 'your-django-secret-key-here';
  return createJWT(payload, secret);
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i]?.replace('--', '');
    const value = args[i + 1];
    
    if (key && value) {
      switch (key) {
        case 'user-id':
          options.userId = parseInt(value);
          break;
        case 'tenant-id':
          options.tenantId = value;
          break;
        case 'document-id':
          options.documentId = value;
          break;
        case 'user-name':
          options.userName = value;
          break;
        case 'expires':
          options.expiresInHours = parseInt(value);
          break;
      }
    }
  }

  const token = generateDemoToken(options);
  
  console.log('🔐 Demo JWT Token Generated');
  console.log('================================');
  console.log(`Token: ${token}`);
  console.log('');
  console.log('📋 Usage Examples:');
  console.log('');
  console.log('1. WebSocket URL with token:');
  console.log(`   ws://localhost:1234/demo-document?token=${token}`);
  console.log('');
  console.log('2. Authorization header:');
  console.log(`   Authorization: Bearer ${token}`);
  console.log('');
  console.log('3. Custom header:');
  console.log(`   X-Auth-Token: ${token}`);
  console.log('');
  console.log('⚙️  Configuration:');
  console.log(`   User ID: ${options.userId || 'random'}`);
  console.log(`   Tenant ID: ${options.tenantId || 'demo-tenant'}`);
  console.log(`   Document ID: ${options.documentId || 'demo-document'}`);
  console.log(`   Expires: ${options.expiresInHours || 24} hours`);
  console.log('');
  console.log('💡 To customize, use:');
  console.log('   node scripts/generate-demo-token.js --user-id 123 --document-id my-doc --expires 1');
}

export { generateDemoToken };
