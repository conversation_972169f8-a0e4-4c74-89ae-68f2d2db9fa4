.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
}

.app-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  font-size: 1.2rem;
  margin-bottom: 20px;
  opacity: 0.9;
}

.document-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.document-controls label {
  font-weight: 500;
  color: white;
}

.document-input {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  min-width: 200px;
}

.document-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.server-info {
  color: rgba(255, 255, 255, 0.8);
  font-family: monospace;
  font-size: 14px;
}

.app-footer {
  margin-top: 40px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  color: white;
}

.instructions,
.features {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.instructions h3,
.features h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.instructions ol,
.features ul {
  text-align: left;
  line-height: 1.6;
}

.instructions li,
.features li {
  margin-bottom: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .App {
    padding: 10px 0;
  }

  .app-container {
    padding: 0 10px;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .document-controls {
    flex-direction: column;
    gap: 10px;
  }

  .document-input {
    min-width: 250px;
  }

  .app-footer {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 30px;
  }

  .instructions,
  .features {
    padding: 15px;
  }
}

/* Authentication Controls */
.auth-controls {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 243, 205, 0.9);
  border-radius: 8px;
  border: 1px solid rgba(255, 234, 167, 0.8);
  backdrop-filter: blur(10px);
}

.auth-controls label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #856404;
  cursor: pointer;
}

.auth-token-input {
  margin-top: 15px;
}

.auth-token-input label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #6c757d;
  cursor: default;
}

.token-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  resize: vertical;
  min-height: 60px;
  background: rgba(255, 255, 255, 0.95);
}
