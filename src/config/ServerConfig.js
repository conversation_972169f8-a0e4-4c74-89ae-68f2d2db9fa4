class ServerConfig {
  constructor() {
    this.config = {
      port: process.env.PORT || 1234,
      host: process.env.HOST || '0.0.0.0',
      cors: {
        origin: process.env.CORS_ORIGIN || '*',
        methods: ['GET', 'POST'],
        credentials: true
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined'
      },
      yjs: {
        persistence: process.env.YJS_PERSISTENCE || false,
        gcEnabled: process.env.YJS_GC_ENABLED !== 'false',
        cleanupInterval: parseInt(process.env.YJS_CLEANUP_INTERVAL) || 300000, // 5 minutes
        maxIdleTime: parseInt(process.env.YJS_MAX_IDLE_TIME) || 1800000 // 30 minutes
      },
      websocket: {
        pingTimeout: parseInt(process.env.WS_PING_TIMEOUT) || 30000,
        maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS) || 1000
      },
      auth: {
        enabled: process.env.AUTH_ENABLED === 'true',
        jwt: {
          secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
          algorithm: process.env.JWT_ALGORITHM || 'HS256',
          issuer: process.env.JWT_ISSUER || 'yjs-server',
          audience: process.env.JWT_AUDIENCE || undefined, // Optional audience validation
          maxAge: process.env.JWT_MAX_AGE || '24h',        // Maximum token age
          maxTokenAge: parseInt(process.env.JWT_MAX_TOKEN_AGE) || 86400 // Max age in seconds
        },
        cache: {
          ttl: parseInt(process.env.AUTH_CACHE_TTL) || 300, // 5 minutes
          enabled: process.env.AUTH_ENABLED === 'true'
        },
        fallback: {
          apiUrl: process.env.AUTH_FALLBACK_API_URL || null,
          enabled: !!process.env.AUTH_FALLBACK_API_URL
        }
      },
      redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        authDb: parseInt(process.env.REDIS_AUTH_DB) || 1
      }
    };
  }

  get(key) {
    return key ? this.config[key] : this.config;
  }

  set(key, value) {
    this.config[key] = value;
  }

  validate() {
    const requiredFields = ['port', 'host'];
    const missing = requiredFields.filter(field => !this.config[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required configuration fields: ${missing.join(', ')}`);
    }
    
    return true;
  }
}

export default ServerConfig;
