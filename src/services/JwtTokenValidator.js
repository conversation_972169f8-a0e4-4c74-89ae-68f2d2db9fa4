import jwt from 'jsonwebtoken';
import ITokenValidator from '../interfaces/ITokenValidator.js';

/**
 * JWT Token Validator implementation
 * Follows Single Responsibility Principle (SRP)
 */
class JwtTokenValidator extends ITokenValidator {
  constructor(config, logger) {
    super();
    this.config = config;
    this.logger = logger;
    this.jwtConfig = config.get('auth').jwt;
  }

  /**
   * Validates JWT token signature and expiration
   * @param {string} token - JWT token to validate
   * @returns {Promise<Object>} Decoded token payload
   */
  async validate(token) {
    try {
      if (!token) {
        throw new Error('Token is required');
      }

      const payload = jwt.verify(token, this.jwtConfig.secret, {
        algorithms: [this.jwtConfig.algorithm],
        issuer: this.jwtConfig.issuer
      });

      if (this.isExpired(payload)) {
        throw new Error('Token has expired');
      }

      this.logger.debug('JWT token validated successfully', {
        userId: payload.user_id,
        tenantId: payload.tenant_id,
        documentId: payload.document_id
      });

      return payload;
    } catch (error) {
      this.logger.warn('JWT token validation failed', {
        error: error.message,
        token: token ? `${token.substring(0, 20)}...` : 'null'
      });
      throw new Error(`Token validation failed: ${error.message}`);
    }
  }

  /**
   * Extracts token from request headers
   * @param {Object} headers - Request headers
   * @returns {string|null} Extracted token or null
   */
  extractToken(headers) {
    try {
      // Check Authorization header (Bearer token)
      const authHeader = headers.authorization || headers.Authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
      }

      // Check custom header
      const customHeader = headers['x-auth-token'] || headers['X-Auth-Token'];
      if (customHeader) {
        return customHeader;
      }

      // Check query parameter (for WebSocket connections)
      const url = headers.url || '';
      const urlParams = new URLSearchParams(url.split('?')[1] || '');
      const tokenFromQuery = urlParams.get('token');
      if (tokenFromQuery) {
        return tokenFromQuery;
      }

      return null;
    } catch (error) {
      this.logger.warn('Failed to extract token from headers', {
        error: error.message,
        headers: Object.keys(headers)
      });
      return null;
    }
  }

  /**
   * Checks if token is expired
   * @param {Object} payload - Decoded token payload
   * @returns {boolean} True if token is expired
   */
  isExpired(payload) {
    if (!payload.exp) {
      return false; // No expiration claim
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  }

  /**
   * Validates token structure and required claims
   * @param {Object} payload - Decoded token payload
   * @returns {boolean} True if token structure is valid
   */
  validateTokenStructure(payload) {
    const requiredClaims = ['user_id', 'tenant_id', 'document_id', 'permissions'];
    
    for (const claim of requiredClaims) {
      if (!payload.hasOwnProperty(claim)) {
        this.logger.warn('Missing required claim in JWT token', {
          missingClaim: claim,
          availableClaims: Object.keys(payload)
        });
        return false;
      }
    }

    // Validate permissions array
    if (!Array.isArray(payload.permissions)) {
      this.logger.warn('Invalid permissions format in JWT token', {
        permissions: payload.permissions,
        type: typeof payload.permissions
      });
      return false;
    }

    return true;
  }

  /**
   * Gets token expiration time
   * @param {Object} payload - Decoded token payload
   * @returns {Date|null} Expiration date or null
   */
  getExpirationDate(payload) {
    if (!payload.exp) {
      return null;
    }
    return new Date(payload.exp * 1000);
  }

  /**
   * Gets time until token expires
   * @param {Object} payload - Decoded token payload
   * @returns {number} Seconds until expiration, -1 if expired
   */
  getTimeUntilExpiration(payload) {
    if (!payload.exp) {
      return Infinity;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp - currentTime;
  }
}

export default JwtTokenValidator;
