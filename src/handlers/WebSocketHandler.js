import { docs } from '../utils/y-websocket-utils.js';

class WebSocketHandler {
  constructor(connectionManager, documentManager, logger) {
    this.connectionManager = connectionManager;
    this.documentManager = documentManager;
    this.logger = logger;
  }

  handleConnection(ws, req) {
    const url = new URL(req.url, `http://${req.headers.host}`);
    const documentId = url.pathname.slice(1) || 'default';
    const userId = url.searchParams.get('userId') || `user-${Date.now()}`;

    const connectionId = `ws-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    const connection = this.connectionManager.addConnection(connectionId, ws, {
      documentId,
      userId,
      origin: req.headers.origin
    });

    ws.on('close', () => {
      this.handleDisconnection(connectionId, 'connection closed');
    });

    ws.on('error', (error) => {
      this.logger.error('WebSocket error', error, { connectionId, documentId });
      this.handleDisconnection(connectionId, 'error');
    });

    return connection;
  }

  handleDisconnection(connectionId, reason) {
    try {
      const connection = this.connectionManager.getConnection(connectionId);
      if (!connection) {
        return;
      }

      const { documentId } = connection;
      this.connectionManager.removeConnection(connectionId);

      const doc = docs.get(documentId);
      if (doc) {
        this.documentManager.updateConnectionCount(documentId, doc.conns.size);
      }
    } catch (error) {
      this.logger.error('Failed to handle disconnection', error, { connectionId, reason });
    }
  }

  getConnectionStats() {
    try {
      const totalConnections = this.connectionManager.getConnectionCount();
      const documentStats = new Map();

      docs.forEach((doc, documentId) => {
        documentStats.set(documentId, {
          connectionCount: doc.conns.size,
          awarenessStates: doc.awareness.getStates().size
        });
      });

      return {
        totalConnections,
        documentStats: Object.fromEntries(documentStats)
      };
    } catch (error) {
      this.logger.error('Failed to get connection stats', error);
      throw error;
    }
  }

  getDocumentInfo(documentId) {
    try {
      const doc = docs.get(documentId);
      if (!doc) {
        return null;
      }

      const connections = this.connectionManager.getConnectionsByDocument(documentId);

      return {
        documentId,
        connectionCount: doc.conns.size,
        awarenessStates: doc.awareness.getStates().size,
        connections: connections.map(conn => ({
          id: conn.id,
          userId: conn.userId,
          joinedAt: conn.joinedAt,
          lastActivity: conn.lastActivity
        }))
      };
    } catch (error) {
      this.logger.error('Failed to get document info', error, { documentId });
      throw error;
    }
  }

  forceDisconnect(connectionId) {
    try {
      const connection = this.connectionManager.getConnection(connectionId);
      if (!connection) {
        throw new Error('Connection not found');
      }

      if (connection.ws && connection.ws.readyState === 1) {
        connection.ws.close(1000, 'Force disconnect');
      }

      this.handleDisconnection(connectionId, 'force disconnect');
      return true;
    } catch (error) {
      this.logger.error('Failed to force disconnect', error, { connectionId });
      throw error;
    }
  }

  sendToConnection(connectionId, message) {
    try {
      const connection = this.connectionManager.getConnection(connectionId);
      if (!connection || !connection.ws) {
        throw new Error('Connection not found or invalid');
      }

      if (connection.ws.readyState === 1) {
        connection.ws.send(JSON.stringify(message));
        this.connectionManager.updateLastActivity(connectionId);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error('Failed to send message to connection', error, { connectionId });
      throw error;
    }
  }

  healthCheck() {
    try {
      const stats = this.getConnectionStats();
      return {
        status: 'healthy',
        totalConnections: stats.totalConnections,
        totalDocuments: docs.size,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

export default WebSocketHandler;
