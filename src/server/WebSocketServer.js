import express from 'express';
import http from 'http';
import { WebSocketServer as WSServer } from 'ws';
import cors from 'cors';
import helmet from 'helmet';
import AuthFactory from '../factories/AuthFactory.js';

class WebSocketServer {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = null;
    this.isRunning = false;
    this.yjsService = null;
    this.authFactory = null;
    this.authMiddleware = null;
    this.authenticatedConnections = new Map(); // Track authenticated connections
  }

  async initialize() {
    try {
      this.app.use(helmet({
        contentSecurityPolicy: false,
        crossOriginEmbedderPolicy: false
      }));

      this.app.use(cors(this.config.get('cors')));
      this.app.use(express.json({ limit: '10mb' }));
      this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

      this.app.use((req, _, next) => {
        this.logger.http(`${req.method} ${req.url}`, {
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        next();
      });

      this.app.use('/public', express.static('public'));

      // Initialize authentication components
      await this.initializeAuth();

      this.app.get('/health', (_, res) => {
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          websocket: this.wss ? 'active' : 'inactive',
          auth: this.authMiddleware ? this.authMiddleware.getStats() : { enabled: false }
        });
      });

      this.setupApiRoutes();
      this.setupErrorHandling();
    } catch (error) {
      this.logger.error('Failed to initialize Express server', error);
      throw error;
    }
  }

  /**
   * Initialize authentication components
   * @returns {Promise<void>}
   */
  async initializeAuth() {
    try {
      this.authFactory = new AuthFactory(this.config, this.logger);

      // Validate configuration first
      const configValidation = this.authFactory.validateConfiguration();
      if (!configValidation.valid) {
        this.logger.warn('Authentication configuration issues detected', {
          issues: configValidation.issues
        });
      }

      // Create auth components
      const authComponents = await this.authFactory.createAuthComponents();
      this.authMiddleware = authComponents.authMiddleware;

      // Set up permission update handler
      this.authFactory.setPermissionUpdateHandler((channel, updateData) => {
        this.handlePermissionUpdate(channel, updateData);
      });

      this.logger.info('Authentication initialized successfully', {
        enabled: authComponents.authService.isEnabled()
      });

    } catch (error) {
      this.logger.error('Failed to initialize authentication', error);
      // Continue without auth if initialization fails
      this.authMiddleware = null;
    }
  }

  setupApiRoutes() {
    const apiRouter = express.Router();

    apiRouter.get('/stats', async (_, res) => {
      try {
        const stats = {};

        if (this.yjsService) {
          stats.yjs = this.yjsService.getStats();
        } else {
          stats.yjs = { error: 'YJS service not available' };
        }

        if (this.authFactory) {
          stats.auth = await this.authFactory.getStats();
        } else {
          stats.auth = { enabled: false };
        }

        stats.connections = {
          total: this.authenticatedConnections.size,
          authenticated: Array.from(this.authenticatedConnections.values())
            .filter(conn => conn.userContext.isAuthenticated).length
        };

        res.json(stats);
      } catch (error) {
        this.logger.error('Failed to get stats', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Auth health check endpoint
    apiRouter.get('/auth/health', async (_, res) => {
      try {
        if (this.authFactory) {
          const health = await this.authFactory.healthCheck();
          res.json(health);
        } else {
          res.json({ status: 'disabled', enabled: false });
        }
      } catch (error) {
        this.logger.error('Failed to get auth health', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    this.app.use('/api', apiRouter);
  }

  setupErrorHandling() {
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.url} not found`
      });
    });

    this.app.use((error, req, res, _) => {
      this.logger.error('Unhandled error in Express', error, {
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      });
    });
  }

  initializeWebSocket() {
    try {
      this.wss = new WSServer({ noServer: true });

      this.wss.on('connection', async (ws, req) => {
        await this.handleWebSocketConnection(ws, req);
      });

      this.server.on('upgrade', async (request, socket, head) => {
        try {
          // Authenticate the connection before upgrading
          if (this.authMiddleware) {
            const authResult = await this.authMiddleware.authenticate(request);

            if (!authResult.success) {
              this.logger.warn('WebSocket authentication failed', {
                error: authResult.error,
                code: authResult.code,
                url: request.url,
                origin: request.headers.origin
              });

              // Close connection with appropriate error code
              const errorCode = this.getWebSocketErrorCode(authResult.code);
              socket.write(`HTTP/1.1 ${errorCode} ${authResult.error}\r\n\r\n`);
              socket.destroy();
              return;
            }

            // Store auth result for use in connection handler
            request.authResult = authResult;
          }

          this.wss.handleUpgrade(request, socket, head, (ws) => {
            this.wss.emit('connection', ws, request);
          });
        } catch (error) {
          this.logger.error('WebSocket upgrade failed', error, {
            url: request.url,
            origin: request.headers.origin
          });
          socket.write('HTTP/1.1 500 Internal Server Error\r\n\r\n');
          socket.destroy();
        }
      });

      return this.wss;
    } catch (error) {
      this.logger.error('Failed to initialize WebSocket server', error);
      throw error;
    }
  }

  /**
   * Handles new WebSocket connections with authentication
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} req - HTTP request object
   * @returns {Promise<void>}
   */
  async handleWebSocketConnection(ws, req) {
    const connectionId = `ws-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    try {
      // Get authentication result from upgrade handler
      const authResult = req.authResult || {
        success: true,
        userContext: { isAuthenticated: false, isGuest: true },
        reason: 'auth_disabled'
      };

      const { userContext } = authResult;
      const url = req.url || '';
      const documentId = userContext.currentDocumentId || url.slice(1).split('?')[0] || 'default';

      // Store authenticated connection
      this.authenticatedConnections.set(connectionId, {
        ws,
        userContext,
        documentId,
        connectedAt: new Date(),
        lastActivity: new Date()
      });

      // Add to YJS connection manager
      if (this.yjsService && this.yjsService.connectionManager) {
        try {
          this.yjsService.connectionManager.addConnection(connectionId, ws, {
            documentId,
            userId: userContext.userId,
            tenantId: userContext.tenantId,
            url: req.url,
            origin: req.headers.origin,
            isAuthenticated: userContext.isAuthenticated,
            permissions: userContext.permissions
          });
        } catch (error) {
          this.logger.error('Failed to add connection to ConnectionManager', error, {
            connectionId,
            documentId
          });
        }
      }

      // Set up connection event handlers
      ws.on('close', () => {
        this.handleWebSocketDisconnection(connectionId, 'connection_closed');
      });

      ws.on('error', (error) => {
        this.logger.error('WebSocket error', error, {
          connectionId,
          userId: userContext.userId,
          documentId
        });
        this.handleWebSocketDisconnection(connectionId, 'connection_error');
      });

      // Set up Y.js WebSocket connection
      const { setupWSConnection } = await import('../utils/y-websocket-utils.js');
      setupWSConnection(ws, req);

      this.logger.info('WebSocket connection established', {
        connectionId,
        userId: userContext.userId,
        tenantId: userContext.tenantId,
        documentId,
        isAuthenticated: userContext.isAuthenticated,
        permissions: userContext.permissions
      });

    } catch (error) {
      this.logger.error('Failed to handle WebSocket connection', error, {
        connectionId,
        url: req.url
      });

      // Clean up and close connection
      this.authenticatedConnections.delete(connectionId);
      if (ws.readyState === ws.OPEN) {
        ws.close(1011, 'Server error during connection setup');
      }
    }
  }

  /**
   * Handles WebSocket disconnections
   * @param {string} connectionId - Connection ID
   * @param {string} reason - Disconnection reason
   */
  handleWebSocketDisconnection(connectionId, reason) {
    try {
      const connection = this.authenticatedConnections.get(connectionId);
      if (!connection) {
        return;
      }

      const { userContext, documentId } = connection;

      // Remove from YJS connection manager
      if (this.yjsService && this.yjsService.connectionManager) {
        try {
          this.yjsService.connectionManager.removeConnection(connectionId);
        } catch (error) {
          this.logger.error('Failed to remove connection from ConnectionManager', error, {
            connectionId
          });
        }
      }

      // Remove from authenticated connections
      this.authenticatedConnections.delete(connectionId);

      this.logger.info('WebSocket connection closed', {
        connectionId,
        userId: userContext.userId,
        tenantId: userContext.tenantId,
        documentId,
        reason,
        duration: Date.now() - connection.connectedAt.getTime()
      });

    } catch (error) {
      this.logger.error('Failed to handle WebSocket disconnection', error, {
        connectionId,
        reason
      });
    }
  }

  /**
   * Handles permission updates from Redis pub/sub
   * @param {string} channel - Redis channel
   * @param {Object} updateData - Permission update data
   */
  handlePermissionUpdate(channel, updateData) {
    try {
      const { user_id, action, document_id, tenant_id } = updateData;

      this.logger.info('Processing permission update', {
        channel,
        userId: user_id,
        tenantId: tenant_id,
        documentId: document_id,
        action
      });

      // Find connections for this user and document
      const affectedConnections = Array.from(this.authenticatedConnections.entries())
        .filter(([_, conn]) => {
          return conn.userContext.userId === user_id &&
                 conn.userContext.tenantId === tenant_id &&
                 (document_id === '*' || conn.documentId === document_id);
        });

      if (action === 'revoke') {
        // Immediately disconnect affected connections
        affectedConnections.forEach(([connectionId, conn]) => {
          this.logger.info('Disconnecting user due to permission revocation', {
            connectionId,
            userId: user_id,
            documentId: conn.documentId
          });

          if (conn.ws && conn.ws.readyState === conn.ws.OPEN) {
            conn.ws.close(1008, 'Permissions revoked');
          }

          this.handleWebSocketDisconnection(connectionId, 'permissions_revoked');
        });
      }

    } catch (error) {
      this.logger.error('Failed to handle permission update', error, {
        channel,
        updateData
      });
    }
  }

  /**
   * Gets WebSocket error code for authentication failures
   * @param {string} authCode - Authentication error code
   * @returns {number} HTTP status code
   */
  getWebSocketErrorCode(authCode) {
    const errorCodes = {
      'MISSING_TOKEN': 401,
      'ACCESS_DENIED': 403,
      'AUTH_ERROR': 401
    };
    return errorCodes[authCode] || 500;
  }

  /**
   * Disconnects a specific user from a document
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @param {string} reason - Disconnection reason
   * @returns {number} Number of connections disconnected
   */
  disconnectUser(userId, documentId, reason = 'admin_disconnect') {
    let disconnectedCount = 0;

    try {
      const connectionsToDisconnect = Array.from(this.authenticatedConnections.entries())
        .filter(([_, conn]) => {
          return conn.userContext.userId === userId &&
                 (documentId === '*' || conn.documentId === documentId);
        });

      connectionsToDisconnect.forEach(([connectionId, conn]) => {
        this.logger.info('Admin disconnecting user', {
          connectionId,
          userId,
          documentId: conn.documentId,
          reason
        });

        if (conn.ws && conn.ws.readyState === conn.ws.OPEN) {
          conn.ws.close(1008, reason);
        }

        this.handleWebSocketDisconnection(connectionId, reason);
        disconnectedCount++;
      });

      return disconnectedCount;
    } catch (error) {
      this.logger.error('Failed to disconnect user', error, {
        userId,
        documentId,
        reason
      });
      return 0;
    }
  }

  setYjsService(yjsService) {
    this.yjsService = yjsService;
  }

  async start() {
    try {
      const port = this.config.get('port');
      const host = this.config.get('host');

      await new Promise((resolve, reject) => {
        this.server.listen(port, host, (error) => {
          if (error) {
            reject(error);
          } else {
            this.isRunning = true;
            this.logger.info(`Server started on ${host}:${port}`);
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.error('Failed to start server', error);
      throw error;
    }
  }

  async stop() {
    try {
      if (!this.isRunning) {
        return;
      }

      this.logger.info('Stopping WebSocket server...');

      // Close all authenticated connections
      this.authenticatedConnections.forEach((conn) => {
        if (conn.ws && conn.ws.readyState === conn.ws.OPEN) {
          conn.ws.close(1001, 'Server shutting down');
        }
      });
      this.authenticatedConnections.clear();

      // Close WebSocket server
      if (this.wss) {
        this.wss.close();
      }

      // Shutdown authentication components
      if (this.authFactory) {
        await this.authFactory.shutdown();
      }

      // Close HTTP server
      await new Promise((resolve) => {
        this.server.close(() => {
          this.isRunning = false;
          this.logger.info('WebSocket server stopped successfully');
          resolve();
        });
      });
    } catch (error) {
      this.logger.error('Failed to stop server', error);
      throw error;
    }
  }

  getWebSocketServer() {
    return this.wss;
  }

  isServerRunning() {
    return this.isRunning;
  }
}

export default WebSocketServer;
