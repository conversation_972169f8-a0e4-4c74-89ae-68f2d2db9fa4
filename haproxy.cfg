# HAProxy Configuration for Y.js WebSocket Load Balancing
# Supports sticky sessions and WebSocket upgrades

global
    daemon
    log stdout local0 info
    maxconn 4096
    user haproxy
    group haproxy

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option redispatch
    retries 3
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    timeout tunnel 3600000ms
    timeout http-keep-alive 10000ms
    timeout http-request 10000ms

# Frontend for incoming connections
frontend yjs_frontend
    bind *:1234
    
    # WebSocket upgrade detection
    acl is_websocket hdr(Upgrade) -i websocket
    acl is_websocket_connection hdr_beg(Connection) -i upgrade
    
    # Route WebSocket connections to backend
    use_backend yjs_websocket_backend if is_websocket is_websocket_connection
    
    # Route HTTP requests to backend
    default_backend yjs_http_backend

# Backend for WebSocket connections (sticky sessions)
backend yjs_websocket_backend
    balance source
    hash-type consistent
    
    # Health check for WebSocket
    option httpchk GET /health
    
    # Y.js server instances
    server yjs1 yjs-server-1:1234 check
    server yjs2 yjs-server-2:1234 check

# Backend for HTTP requests (round-robin)
backend yjs_http_backend
    balance roundrobin
    
    # Health check for HTTP
    option httpchk GET /health
    
    # Y.js server instances
    server yjs1 yjs-server-1:1234 check
    server yjs2 yjs-server-2:1234 check

# Statistics page
listen stats
    bind *:8080
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
