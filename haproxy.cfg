# HAProxy Configuration for Y.js WebSocket Load Balancing
# Supports sticky sessions and WebSocket upgrades

global
    daemon
    log stdout local0 info
    maxconn 4096
    user haproxy
    group haproxy

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option redispatch
    retries 3
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    timeout tunnel 1h
    timeout http-keep-alive 10s
    timeout http-request 10s

# Frontend - Main entry point
frontend yjs_frontend
    bind *:1234
    
    # Enable WebSocket support
    option httpclose
    option forwardfor
    
    # Capture WebSocket upgrade headers
    capture request header Upgrade len 10
    capture request header Connection len 10
    
    # Route WebSocket connections with sticky sessions
    acl is_websocket hdr(Upgrade) -i websocket
    acl is_websocket_conn hdr_beg(Connection) -i upgrade
    
    # Use sticky sessions based on document ID in URL path
    stick-table type string len 64 size 100k expire 30m
    stick on path
    
    # Route to backend
    default_backend yjs_servers

# Backend - Y.js server instances
backend yjs_servers
    balance roundrobin
    
    # Enable sticky sessions
    stick-table type string len 64 size 100k expire 30m
    stick on path
    
    # Health check configuration
    option httpchk GET /health
    http-check expect status 200
    
    # Server instances
    server yjs-server-1 yjs-server-1:1234 check inter 10s fall 3 rise 2 weight 100
    server yjs-server-2 yjs-server-2:1234 check inter 10s fall 3 rise 2 weight 100
    server yjs-server-3 yjs-server-3:1234 check inter 10s fall 3 rise 2 weight 100 backup
    
    # WebSocket timeout settings
    timeout tunnel 1h
    timeout server 50s

# Statistics interface
listen stats
    bind *:8080
    stats enable
    stats uri /stats
    stats refresh 30s
    stats show-node
    stats show-legends
    
    # Basic auth for stats (optional)
    # stats auth admin:password
    
    # Show detailed server information
    stats show-desc "Y.js WebSocket Load Balancer"
    stats admin if TRUE

# Health check endpoint
frontend health_check
    bind *:8090
    monitor-uri /health
    monitor fail if { nbsrv(yjs_servers) lt 1 }

# Error pages
errorfile 400 /usr/local/etc/haproxy/errors/400.http
errorfile 403 /usr/local/etc/haproxy/errors/403.http
errorfile 408 /usr/local/etc/haproxy/errors/408.http
errorfile 500 /usr/local/etc/haproxy/errors/500.http
errorfile 502 /usr/local/etc/haproxy/errors/502.http
errorfile 503 /usr/local/etc/haproxy/errors/503.http
errorfile 504 /usr/local/etc/haproxy/errors/504.http
