<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tiptap Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .editor {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            margin: 20px 0;
        }
        .toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .toolbar button {
            padding: 5px 10px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
        }
        .toolbar button:hover {
            background: #e9ecef;
        }
        .toolbar button.active {
            background: #007bff;
            color: white;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Tiptap Loading Test</h1>
    
    <div class="log" id="log">Loading Tiptap libraries...</div>
    
    <div class="toolbar">
        <button id="boldBtn">Bold</button>
        <button id="italicBtn">Italic</button>
        <button id="underlineBtn">Underline</button>
        <button id="h1Btn">H1</button>
        <button id="h2Btn">H2</button>
    </div>
    
    <div id="editor" class="editor"></div>
    
    <button onclick="testEditor()">Test Editor</button>

    <!-- Tiptap Scripts - Using official vanilla JS approach -->
    <script src="https://unpkg.com/@tiptap/core@2.1.13"></script>
    <script src="https://unpkg.com/@tiptap/starter-kit@2.1.13"></script>
    <script src="https://unpkg.com/@tiptap/extension-underline@2.1.13"></script>

    <script>
        let editor = null;
        
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `\n[${timestamp}] ${message}`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }
        
        function initializeTiptap() {
            log('🔍 Checking for Tiptap libraries...');
            
            // Check for Tiptap globals using vanilla JS approach
            const globals = Object.keys(window).filter(k =>
                k.includes('tiptap') || k.includes('Tiptap')
            );
            log(`Available globals: ${globals.join(', ')}`);

            // Use vanilla JS approach from official docs
            if (typeof window.TiptapCore !== 'undefined' &&
                typeof window.TiptapStarterKit !== 'undefined') {
                try {
                    log('✅ Found Tiptap libraries, creating editor...');

                    // Get classes from globals (vanilla JS approach)
                    const { Editor } = window.TiptapCore;
                    const { StarterKit } = window.TiptapStarterKit;
                    const { Underline } = window.TiptapUnderline || {};

                    log(`Editor class: ${typeof Editor}`);
                    log(`StarterKit class: ${typeof StarterKit}`);
                    log(`Underline class: ${typeof Underline}`);
                    
                    if (Editor && StarterKit) {
                        editor = new Editor({
                            element: document.getElementById('editor'),
                            extensions: [
                                StarterKit,
                                ...(Underline ? [Underline] : [])
                            ],
                            content: '<p>Hello World! This is a <strong>Tiptap</strong> editor.</p>',
                            onUpdate: () => {
                                log('📝 Editor content updated');
                            }
                        });
                        
                        log('🎉 Tiptap editor created successfully!');
                        setupToolbar();
                    } else {
                        log('❌ Editor or StarterKit classes not found');
                        createFallbackEditor();
                    }
                } catch (error) {
                    log(`❌ Error creating Tiptap editor: ${error.message}`);
                    createFallbackEditor();
                }
            } else {
                log('❌ Tiptap libraries not found');
                createFallbackEditor();
            }
        }
        
        function createFallbackEditor() {
            log('🔧 Creating fallback contenteditable editor...');
            const editorEl = document.getElementById('editor');
            editorEl.contentEditable = true;
            editorEl.innerHTML = '<p>Fallback editor - Tiptap failed to load</p>';
            setupFallbackToolbar();
        }
        
        function setupToolbar() {
            document.getElementById('boldBtn').onclick = () => {
                editor?.chain().focus().toggleBold().run();
                updateToolbar();
            };
            document.getElementById('italicBtn').onclick = () => {
                editor?.chain().focus().toggleItalic().run();
                updateToolbar();
            };
            document.getElementById('underlineBtn').onclick = () => {
                editor?.chain().focus().toggleUnderline().run();
                updateToolbar();
            };
            document.getElementById('h1Btn').onclick = () => {
                editor?.chain().focus().toggleHeading({ level: 1 }).run();
                updateToolbar();
            };
            document.getElementById('h2Btn').onclick = () => {
                editor?.chain().focus().toggleHeading({ level: 2 }).run();
                updateToolbar();
            };
            
            updateToolbar();
        }
        
        function setupFallbackToolbar() {
            document.getElementById('boldBtn').onclick = () => {
                document.execCommand('bold');
            };
            document.getElementById('italicBtn').onclick = () => {
                document.execCommand('italic');
            };
            document.getElementById('underlineBtn').onclick = () => {
                document.execCommand('underline');
            };
            document.getElementById('h1Btn').onclick = () => {
                document.execCommand('formatBlock', false, 'h1');
            };
            document.getElementById('h2Btn').onclick = () => {
                document.execCommand('formatBlock', false, 'h2');
            };
        }
        
        function updateToolbar() {
            if (!editor) return;
            
            document.getElementById('boldBtn').classList.toggle('active', editor.isActive('bold'));
            document.getElementById('italicBtn').classList.toggle('active', editor.isActive('italic'));
            document.getElementById('underlineBtn').classList.toggle('active', editor.isActive('underline'));
            document.getElementById('h1Btn').classList.toggle('active', editor.isActive('heading', { level: 1 }));
            document.getElementById('h2Btn').classList.toggle('active', editor.isActive('heading', { level: 2 }));
        }
        
        function testEditor() {
            if (editor) {
                const content = editor.getHTML();
                log(`📄 Current content: ${content}`);
                editor.commands.setContent('<p>Test content inserted!</p>');
            } else {
                log('❌ No editor available');
            }
        }
        
        // Initialize after a delay to ensure scripts are loaded
        setTimeout(() => {
            initializeTiptap();
        }, 1000);
    </script>
</body>
</html>
