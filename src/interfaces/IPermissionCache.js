/**
 * Interface for permission caching
 * Follows Single Responsibility Principle (SRP)
 */
class IPermissionCache {
  /**
   * Gets cached permissions for a user and document
   * @param {string} tenantId - Tenant ID
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @returns {Promise<Object|null>} Cached permissions or null
   */
  async get(tenantId, userId, documentId) {
    throw new Error('Method get must be implemented');
  }

  /**
   * Sets cached permissions for a user and document
   * @param {string} tenantId - Tenant ID
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @param {Object} permissions - Permission object to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<void>}
   */
  async set(tenantId, userId, documentId, permissions, ttl = 300) {
    throw new Error('Method set must be implemented');
  }

  /**
   * Deletes cached permissions
   * @param {string} tenantId - Tenant ID
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID (optional)
   * @returns {Promise<void>}
   */
  async delete(tenantId, userId, documentId = null) {
    throw new Error('Method delete must be implemented');
  }

  /**
   * Subscribes to permission update events
   * @param {Function} callback - Callback function for permission updates
   * @returns {Promise<void>}
   */
  async subscribeToUpdates(callback) {
    throw new Error('Method subscribeToUpdates must be implemented');
  }

  /**
   * Publishes permission update event
   * @param {string} tenantId - Tenant ID
   * @param {string} documentId - Document ID
   * @param {Object} updateData - Update data
   * @returns {Promise<void>}
   */
  async publishUpdate(tenantId, documentId, updateData) {
    throw new Error('Method publishUpdate must be implemented');
  }

  /**
   * Checks if caching is enabled
   * @returns {boolean} True if caching is enabled
   */
  isEnabled() {
    throw new Error('Method isEnabled must be implemented');
  }
}

export default IPermissionCache;
