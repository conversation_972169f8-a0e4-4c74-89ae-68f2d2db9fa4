/* Main Editor Container */
.tiptap-editor {
  max-width: 760px;
  margin: 36px auto 32px auto;
  background: linear-gradient(135deg, #f9fbff 70%, #e7f2ff 100%);
  border-radius: 22px;
  box-shadow: 0 8px 32px rgba(42,56,105,0.13), 0 2px 6px rgba(89,108,167,0.06);
  padding: 0 0 30px 0;
  overflow: hidden;
  border: 1.5px solid #e3e9f7;
  transition: box-shadow 0.22s cubic-bezier(.45,1.2,.56,1);
}

.tiptap-editor:hover {
  box-shadow: 0 12px 36px rgba(42,56,105,0.16), 0 2px 6px rgba(89,108,167,0.09);
}

/* Header */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 32px 38px 12px 38px;
  background: linear-gradient(90deg, #eaf4fd 0%, #f7fafd 100%);
  border-bottom: 1.5px solid #e3e9f7;
  box-shadow: 0 2px 12px rgba(42,56,105,0.04);
}

.editor-header h2 {
  margin: 0;
  color: #243e61;
  font-size: 29px;
  font-weight: 800;
  letter-spacing: -1.5px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.editor-header h2::before {
  content: "🌟";
  font-size: 1.5em;
  margin-right: 8px;
}

.connection-info {
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 15px;
  flex-wrap: wrap;
  background: #f2f8ff;
  border-radius: 8px;
  padding: 3px 12px;
}

/* Status badges */
.status {
  padding: 4px 13px;
  border-radius: 99px;
  font-weight: 700;
  font-size: 13.2px;
  letter-spacing: 0.02em;
  box-shadow: 0 1px 4px rgba(42,56,105,0.06);
  border: 1px solid #e3e9f7;
  background: #f9f9f9;
}
.status.connected {
  background: #dbffe7;
  color: #238b4e;
  border-color: #79edb6;
}
.status.connecting {
  background: #fffbe5;
  color: #a97a00;
  border-color: #ffe799;
}
.status.disconnected {
  background: #fff0f0;
  color: #b13636;
  border-color: #ffb2b2;
}

.document-id {
  background: #f4f8ff;
  color: #4871c7;
  padding: 3.5px 11px;
  border-radius: 5px;
  font-family: monospace;
  font-size: 12.5px;
  border: 1px solid #e0e8f7;
}

.users-count {
  background: linear-gradient(90deg, #edf8fd 60%, #e6f5ff 100%);
  color: #316ee0;
  padding: 3.5px 12px;
  border-radius: 5px;
  font-weight: 700;
  font-size: 13.5px;
  border: 1px solid #d2e4fb;
}

/* Toolbar */
.toolbar {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 19px 38px 0 38px;
  background: transparent;
  flex-wrap: wrap;
  border-bottom: 1px solid #e3e9f7;
  background: linear-gradient(90deg, #fafdff 70%, #e8f4ff 100%);
  box-shadow: 0 2px 7px 0 rgba(89,108,167,0.05);
}

.toolbar button {
  background: #ffffff;
  color: #325196;
  border: 1.5px solid #e3e9f7;
  border-radius: 8px;
  padding: 8px 14px;
  font-size: 16px;
  margin: 0 3px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 1px 3px 0 rgba(42,56,105,0.07);
  outline: none;
  transition: all 0.13s cubic-bezier(.45,1.2,.56,1);
  position: relative;
}
.toolbar button:hover,
.toolbar button:focus {
  background: #e3f2fd;
  color: #1b4ab6;
  border-color: #b8d9fa;
  transform: translateY(-2px) scale(1.04);
  z-index: 1;
}
.toolbar button.is-active {
  background: linear-gradient(90deg, #3778ff 80%, #53a0fd 100%);
  color: #fff;
  border-color: #3778ff;
  box-shadow: 0 2px 10px 0 rgba(43,107,235,0.10);
  transform: scale(1.08);
  z-index: 2;
}
.toolbar .separator {
  width: 2px;
  height: 28px;
  background: #e3e9f7;
  margin: 0 14px;
  border-radius: 3px;
}

/* Editor Content */
.editor-container {
  border: none;
  background: #fcfcff;
  margin: 0 38px 0 38px;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 1px 2px rgba(89,108,167,0.03);
  min-height: 400px;
  padding: 0;
}

.tiptap-editor-content {
  padding: 28px 10px 24px 10px;
  min-height: 360px;
  outline: none;
  line-height: 1.85;
  font-size: 17px;
  color: #2a3c5c;
  background: transparent;
  border-radius: 0 0 12px 12px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
.tiptap-editor-content:focus {
  outline: 2px solid #60a3ff;
}

/* Typography */
.tiptap-editor-content h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 1.1em 0 0.5em 0;
  line-height: 1.18;
  letter-spacing: -1px;
  color: #2952b2;
}
.tiptap-editor-content h2 {
  font-size: 1.45em;
  font-weight: 700;
  margin: 1.05em 0 0.5em 0;
  line-height: 1.3;
  color: #327ae3;
}
.tiptap-editor-content h3 {
  font-size: 1.18em;
  font-weight: 700;
  margin: 0.95em 0 0.5em 0;
  line-height: 1.4;
  color: #416db8;
}
.tiptap-editor-content p {
  margin: 0.55em 0;
}

.tiptap-editor-content ul,
.tiptap-editor-content ol {
  padding-left: 1.8em;
  margin: 0.5em 0;
}

.tiptap-editor-content li {
  margin: 0.23em 0;
}

.tiptap-editor-content blockquote {
  border-left: 4px solid #3a91e8;
  padding-left: 1em;
  margin: 1.1em 0;
  font-style: italic;
  color: #4780b6;
  background: #f3faff;
  padding: 1.1em;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 6px rgba(42,56,105,0.04);
}

.tiptap-editor-content pre {
  background: #f7faff;
  border: 1px solid #e9f2ff;
  border-radius: 7px;
  padding: 1.2em;
  overflow-x: auto;
  font-family: 'Fira Mono', 'Courier New', monospace;
  margin: 1.1em 0;
}

.tiptap-editor-content code {
  background: #eef5ff;
  padding: 0.2em 0.44em;
  border-radius: 4px;
  font-family: 'Fira Mono', 'Courier New', monospace;
  font-size: 0.98em;
  color: #2952b2;
}

.tiptap-editor-content pre code {
  background: none;
  padding: 0;
}

/* Collaboration cursor styles */
.collaboration-cursor__caret {
  border-left: 2.5px solid #2172ea;
  margin-left: -1.25px;
  pointer-events: none;
  position: relative;
  z-index: 10;
}
.collaboration-cursor__label {
  border-radius: 3px 3px 3px 0;
  color: #0d0d0d;
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  left: -1px;
  line-height: normal;
  padding: 0.13rem 0.45rem;
  position: absolute;
  top: -1.5em;
  user-select: none;
  white-space: nowrap;
  background: #e3effe;
  box-shadow: 0 2px 8px rgba(42,56,105,0.07);
}

/* Footer */
.editor-footer {
  margin: 0 38px;
  padding-top: 19px;
  border-top: 1px solid #e3e9f7;
  color: #7a8495;
  font-size: 14px;
  text-align: right;
  letter-spacing: 0.01em;
}

/* Active Users (badges) */
.active-users {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 8px;
}
.user-badge {
  padding: 5px 15px;
  border-radius: 999px;
  color: #fff;
  font-size: 13px;
  font-weight: 700;
  background: linear-gradient(90deg, #7bdff2 0%, #3a8dde 100%);
  text-shadow: 0 1px 3px rgba(42,56,105,0.19);
  border: 1.2px solid #bee6fc;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 350px;
  font-size: 19px;
  color: #6c7ba1;
  text-align: center;
  background: linear-gradient(135deg, #f7fafd 70%, #e7f2ff 100%);
  border-radius: 18px;
}
.loading-spinner {
  width: 46px;
  height: 46px;
  border: 5.5px solid #e9f2fd;
  border-top: 5.5px solid #2979ff;
  border-radius: 50%;
  animation: spin 1.08s linear infinite;
  margin-bottom: 20px;
}
@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

/* Responsive */
@media (max-width: 950px) {
  .tiptap-editor { max-width: 99vw; }
  .editor-header,
  .toolbar,
  .editor-container,
  .editor-footer { padding-left: 12px; padding-right: 12px;}
}
@media (max-width: 650px) {
  .tiptap-editor { margin: 6px 0 0 0; }
  .editor-header,
  .toolbar,
  .editor-container,
  .editor-footer { padding-left: 2vw; padding-right: 2vw; }
  .toolbar button { font-size: 12.2px; padding: 5px 8.5px;}
  .editor-header h2 { font-size: 21px; }
}