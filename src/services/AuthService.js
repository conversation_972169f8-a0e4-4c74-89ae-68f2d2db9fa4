import IAuthService from '../interfaces/IAuthService.js';
import fetch from 'node-fetch';

/**
 * Main authentication service
 * Follows Dependency Inversion Principle (DIP) and Open/Closed Principle (OCP)
 */
class AuthService extends IAuthService {
  constructor(config, logger, tokenValidator, permissionCache) {
    super();
    this.config = config;
    this.logger = logger;
    this.tokenValidator = tokenValidator;
    this.permissionCache = permissionCache;
    this.authConfig = config.get('auth');
    this.fallbackConfig = this.authConfig.fallback;
  }

  /**
   * Validates a JWT token and returns user context
   * @param {string} token - JWT token to validate
   * @returns {Promise<Object>} User context with permissions
   */
  async validateToken(token) {
    if (!this.isEnabled()) {
      this.logger.debug('Authentication is disabled, allowing all connections');
      return this.createGuestContext();
    }

    try {
      // Step 1: Validate JWT token
      const payload = await this.tokenValidator.validate(token);
      
      // Step 2: Validate token structure
      if (!this.tokenValidator.validateTokenStructure(payload)) {
        throw new Error('Invalid token structure');
      }

      // Step 3: Create user context
      const userContext = {
        userId: payload.user_id,
        tenantId: payload.tenant_id,
        documentId: payload.document_id,
        permissions: payload.permissions,
        userName: payload.user_name || `User ${payload.user_id}`,
        tokenExpiry: this.tokenValidator.getExpirationDate(payload),
        isAuthenticated: true
      };

      this.logger.info('User authenticated successfully', {
        userId: userContext.userId,
        tenantId: userContext.tenantId,
        documentId: userContext.documentId,
        permissions: userContext.permissions
      });

      return userContext;
    } catch (error) {
      this.logger.warn('Token validation failed', {
        error: error.message,
        token: token ? `${token.substring(0, 20)}...` : 'null'
      });
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Checks if authentication is enabled
   * @returns {boolean} True if auth is enabled
   */
  isEnabled() {
    return this.authConfig.enabled;
  }

  /**
   * Validates user access to a specific document
   * @param {Object} userContext - User context from token validation
   * @param {string} documentId - Document ID to check access for
   * @param {string} action - Action type (read, write, admin)
   * @returns {Promise<boolean>} True if user has access
   */
  async validateDocumentAccess(userContext, documentId, action = 'read') {
    if (!this.isEnabled()) {
      return true; // Allow all access when auth is disabled
    }

    try {
      // Step 1: Check if user is authenticated
      if (!userContext.isAuthenticated) {
        this.logger.warn('Unauthenticated user attempting document access', {
          documentId,
          action
        });
        return false;
      }

      // Step 2: Validate tenant isolation
      if (!this.validateTenantAccess(userContext, documentId)) {
        this.logger.warn('Cross-tenant access attempt blocked', {
          userId: userContext.userId,
          userTenant: userContext.tenantId,
          documentId,
          action
        });
        return false;
      }

      // Step 3: Check cached permissions first
      let permissions = null;
      if (this.permissionCache.isEnabled()) {
        permissions = await this.permissionCache.get(
          userContext.tenantId,
          userContext.userId,
          documentId
        );
      }

      // Step 4: If not cached, use token permissions or fallback to API
      if (!permissions) {
        if (userContext.documentId === documentId) {
          // Token is for this specific document
          permissions = {
            permissions: userContext.permissions,
            documentId: documentId,
            tenantId: userContext.tenantId
          };
        } else if (this.fallbackConfig.enabled) {
          // Fallback to Django API validation
          permissions = await this.validateWithFallbackApi(userContext, documentId, action);
        } else {
          this.logger.warn('No permission source available for document access', {
            userId: userContext.userId,
            documentId,
            tokenDocumentId: userContext.documentId
          });
          return false;
        }

        // Cache the permissions if available
        if (permissions && this.permissionCache.isEnabled()) {
          await this.permissionCache.set(
            userContext.tenantId,
            userContext.userId,
            documentId,
            permissions
          );
        }
      }

      // Step 5: Check if user has required permission
      const hasPermission = this.checkPermission(permissions.permissions, action);
      
      this.logger.debug('Document access validation result', {
        userId: userContext.userId,
        tenantId: userContext.tenantId,
        documentId,
        action,
        hasPermission,
        permissions: permissions.permissions
      });

      return hasPermission;
    } catch (error) {
      this.logger.error('Failed to validate document access', error, {
        userId: userContext.userId,
        documentId,
        action
      });
      return false;
    }
  }

  /**
   * Validates tenant access for multi-tenant isolation
   * @param {Object} userContext - User context
   * @param {string} documentId - Document ID
   * @returns {boolean} True if access is allowed
   */
  validateTenantAccess(userContext, documentId) {
    // Extract tenant from document ID if it follows pattern: tenant:document
    if (documentId.includes(':')) {
      const [docTenant] = documentId.split(':');
      return docTenant === userContext.tenantId;
    }

    // If no tenant prefix, allow access (backward compatibility)
    return true;
  }

  /**
   * Checks if user has specific permission
   * @param {Array} permissions - User permissions array
   * @param {string} action - Required action
   * @returns {boolean} True if user has permission
   */
  checkPermission(permissions, action) {
    if (!Array.isArray(permissions)) {
      return false;
    }

    const permissionMap = {
      'read': ['read', 'write', 'admin'],
      'write': ['write', 'admin'],
      'admin': ['admin']
    };

    const requiredPermissions = permissionMap[action] || [action];
    return requiredPermissions.some(perm => permissions.includes(perm));
  }

  /**
   * Validates permissions using fallback Django API
   * @param {Object} userContext - User context
   * @param {string} documentId - Document ID
   * @param {string} action - Action type
   * @returns {Promise<Object>} Permission object
   */
  async validateWithFallbackApi(userContext, documentId, action) {
    try {
      const response = await fetch(this.fallbackConfig.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userContext.originalToken || ''}`
        },
        body: JSON.stringify({
          user_id: userContext.userId,
          tenant_id: userContext.tenantId,
          document_id: documentId,
          action: action
        }),
        timeout: 5000
      });

      if (!response.ok) {
        throw new Error(`API responded with status ${response.status}`);
      }

      const result = await response.json();
      
      this.logger.debug('Fallback API validation result', {
        userId: userContext.userId,
        documentId,
        action,
        result
      });

      return {
        permissions: result.permissions || [],
        documentId: documentId,
        tenantId: userContext.tenantId,
        source: 'fallback_api'
      };
    } catch (error) {
      this.logger.error('Fallback API validation failed', error, {
        userId: userContext.userId,
        documentId,
        action
      });
      throw error;
    }
  }

  /**
   * Creates guest context when auth is disabled
   * @returns {Object} Guest user context
   */
  createGuestContext() {
    return {
      userId: 'guest',
      tenantId: 'default',
      documentId: null,
      permissions: ['read', 'write'],
      userName: 'Guest User',
      tokenExpiry: null,
      isAuthenticated: false,
      isGuest: true
    };
  }

  /**
   * Invalidates cached permissions for a user
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID (optional)
   * @returns {Promise<void>}
   */
  async invalidateCache(userId, documentId = null) {
    if (!this.permissionCache.isEnabled()) {
      return;
    }

    try {
      // We need tenant ID to invalidate cache, but if not provided, 
      // we'll need to clear all tenants for this user (less efficient)
      if (documentId && documentId.includes(':')) {
        const [tenantId] = documentId.split(':');
        await this.permissionCache.delete(tenantId, userId, documentId);
      } else {
        this.logger.warn('Cache invalidation without tenant context - this may be inefficient', {
          userId,
          documentId
        });
        // This would require scanning all possible tenant keys
        // In production, consider requiring tenantId parameter
      }

      this.logger.info('Cache invalidated for user', {
        userId,
        documentId
      });
    } catch (error) {
      this.logger.error('Failed to invalidate cache', error, {
        userId,
        documentId
      });
    }
  }

  /**
   * Get authentication statistics
   * @returns {Promise<Object>} Auth statistics
   */
  async getStats() {
    const stats = {
      enabled: this.isEnabled(),
      fallbackEnabled: this.fallbackConfig.enabled,
      cacheEnabled: this.permissionCache.isEnabled()
    };

    if (this.permissionCache.isEnabled()) {
      stats.cache = await this.permissionCache.getStats();
    }

    return stats;
  }
}

export default AuthService;
