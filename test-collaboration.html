<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y.js Collaboration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        #editor {
            border: 1px solid #ccc;
            min-height: 200px;
            padding: 10px;
            margin: 10px 0;
        }
        .user-list {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Y.js WebSocket Collaboration Test</h1>
    
    <div id="status" class="status connecting">Connecting...</div>
    
    <div class="user-list">
        <strong>Connected Users:</strong>
        <span id="user-count">0</span>
        <div id="user-list"></div>
    </div>
    
    <div id="editor" contenteditable="true">
        Start typing to test real-time collaboration...
    </div>
    
    <div id="logs" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
        <strong>Logs:</strong><br>
    </div>

    <script src="https://unpkg.com/yjs@13.6.8/dist/yjs.js"></script>
    <script src="https://unpkg.com/y-websocket@1.5.0/dist/y-websocket.js"></script>
    
    <script>
        const CLIENT_ID = Math.floor(Math.random() * 100000);
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `[${timestamp}] ${message}<br>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(`[Client ${CLIENT_ID}] ${message}`);
        }
        
        function updateStatus(status) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }
        
        function updateUsers(users) {
            const userCountEl = document.getElementById('user-count');
            const userListEl = document.getElementById('user-list');
            
            userCountEl.textContent = users.length;
            userListEl.innerHTML = users.map(user => 
                `<div style="margin: 5px 0; padding: 5px; background-color: ${user.color || '#ccc'}; border-radius: 3px; color: white;">
                    ${user.name || 'Anonymous'}
                </div>`
            ).join('');
        }
        
        // Initialize Y.js
        log('Initializing Y.js document and WebSocket provider...');
        const ydoc = new Y.Doc();
        const provider = new WebsocketProvider('ws://localhost:1234', 'demo-document', ydoc);
        
        log(`WebSocket provider created: ${provider.url}`);
        
        // Set up user info
        const userName = `User ${Math.floor(Math.random() * 1000)}`;
        const userColor = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'][Math.floor(Math.random() * 7)];
        
        provider.awareness.setLocalStateField('user', {
            name: userName,
            color: userColor
        });
        
        log(`Set local user: ${userName} (${userColor})`);
        
        // Set up event listeners
        provider.on('status', (event) => {
            log(`WebSocket status: ${event.status}`);
            updateStatus(event.status);
        });
        
        provider.on('synced', (isSynced) => {
            log(`Document synced: ${isSynced}`);
        });
        
        provider.awareness.on('change', () => {
            const states = Array.from(provider.awareness.getStates().values());
            const users = states.map(state => state.user).filter(user => user);
            log(`Awareness change: ${users.length} users - ${users.map(u => u.name).join(', ')}`);
            log(`WebSocket connected: ${provider.wsconnected}, ReadyState: ${provider.ws?.readyState}`);
            updateUsers(users);
        });
        
        // Set up text synchronization
        const ytext = ydoc.getText('content');
        const editor = document.getElementById('editor');
        
        // Update editor when Y.js text changes
        ytext.observe((event) => {
            if (event.transaction.origin !== 'user-input') {
                editor.textContent = ytext.toString();
                log('Text updated from remote');
            }
        });
        
        // Update Y.js when editor changes
        editor.addEventListener('input', () => {
            ydoc.transact(() => {
                ytext.delete(0, ytext.length);
                ytext.insert(0, editor.textContent);
            }, 'user-input');
        });
        
        // Periodic status check
        setInterval(() => {
            const users = Array.from(provider.awareness.getStates().values()).map(s => s.user).filter(u => u);
            log(`Status check - Connected: ${provider.wsconnected}, ReadyState: ${provider.ws?.readyState}, Users: ${users.length}`);
        }, 10000);
        
        log('Y.js collaboration test initialized');
    </script>
</body>
</html>
