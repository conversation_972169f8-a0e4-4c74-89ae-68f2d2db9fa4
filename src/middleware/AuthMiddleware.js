/**
 * Authentication middleware for WebSocket connections
 * Follows Single Responsibility Principle (SRP)
 */
class AuthMiddleware {
  constructor(authService, logger) {
    this.authService = authService;
    this.logger = logger;
  }

  /**
   * Authenticates WebSocket connection
   * @param {Object} request - WebSocket upgrade request
   * @returns {Promise<Object>} Authentication result with user context
   */
  async authenticate(request) {
    try {
      // Step 1: Check if authentication is enabled
      if (!this.authService.isEnabled()) {
        this.logger.debug('Authentication disabled, allowing connection');
        return {
          success: true,
          userContext: this.authService.createGuestContext(),
          reason: 'auth_disabled'
        };
      }

      // Step 2: Extract token from request
      console.log(request)
      const token = this.extractToken(request);
      if (!token) {
        this.logger.warn('No authentication token provided', {
          url: request.url,
          origin: request.headers.origin
        });
        return {
          success: false,
          error: 'No authentication token provided',
          code: 'MISSING_TOKEN'
        };
      }

      // Step 3: Validate token and get user context
      const userContext = await this.authService.validateToken(token);
      
      // Step 4: Extract document ID from URL
      const documentId = this.extractDocumentId(request.url);
      
      // Step 5: Validate document access
      const hasAccess = await this.authService.validateDocumentAccess(
        userContext, 
        documentId, 
        'write' // WebSocket connections typically need write access
      );

      if (!hasAccess) {
        this.logger.warn('User does not have access to document', {
          userId: userContext.userId,
          tenantId: userContext.tenantId,
          documentId,
          permissions: userContext.permissions
        });
        return {
          success: false,
          error: 'Insufficient permissions for document access',
          code: 'ACCESS_DENIED'
        };
      }

      // Step 6: Add document context to user
      userContext.currentDocumentId = documentId;
      userContext.originalToken = token;

      this.logger.info('🔐 WebSocket connection authenticated successfully', {
        userId: userContext.userId,
        tenantId: userContext.tenantId,
        documentId,
        permissions: userContext.permissions,
        userName: userContext.userName,
        origin: request.headers.origin,
        tokenExpiry: userContext.exp ? new Date(userContext.exp * 1000).toISOString() : 'unknown'
      });

      return {
        success: true,
        userContext,
        reason: 'authenticated'
      };

    } catch (error) {
      this.logger.error('Authentication failed', error, {
        url: request.url,
        origin: request.headers.origin
      });

      return {
        success: false,
        error: error.message,
        code: 'AUTH_ERROR'
      };
    }
  }

  /**
   * Extracts authentication token from WebSocket request
   * @param {Object} request - WebSocket upgrade request
   * @returns {string|null} Authentication token or null
   */
  extractToken(request) {
    try {
      // Method 1: Authorization header
      const authHeader = request.headers.authorization || request.headers.Authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
      }

      // Method 2: Custom header
      const customHeader = request.headers['x-auth-token'] || request.headers['X-Auth-Token'];
      if (customHeader) {
        return customHeader;
      }

      // Method 3: Query parameter (for WebSocket connections)
      const url = new URL(request.url, `http://${request.headers.host}`);
      const tokenFromQuery = url.searchParams.get('token');
      if (tokenFromQuery) {
        return tokenFromQuery;
      }

      // Method 4: Cookie (if needed for browser compatibility)
      const cookies = this.parseCookies(request.headers.cookie);
      if (cookies.auth_token) {
        return cookies.auth_token;
      }

      return null;
    } catch (error) {
      this.logger.warn('Failed to extract token from request', {
        error: error.message,
        url: request.url
      });
      return null;
    }
  }

  /**
   * Extracts document ID from WebSocket URL
   * @param {string} url - WebSocket URL
   * @returns {string} Document ID
   */
  extractDocumentId(url) {
    try {
      const urlObj = new URL(url, 'http://localhost');
      const pathname = urlObj.pathname;
      
      // Remove leading slash and extract document ID
      const documentId = pathname.slice(1).split('?')[0] || 'default';
      
      this.logger.debug('Extracted document ID from URL', {
        url,
        documentId
      });
      
      return documentId;
    } catch (error) {
      this.logger.warn('Failed to extract document ID from URL', {
        error: error.message,
        url
      });
      return 'default';
    }
  }

  /**
   * Parses cookies from cookie header
   * @param {string} cookieHeader - Cookie header value
   * @returns {Object} Parsed cookies
   */
  parseCookies(cookieHeader) {
    const cookies = {};
    
    if (!cookieHeader) {
      return cookies;
    }

    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });

    return cookies;
  }

  /**
   * Validates origin for CORS protection
   * @param {Object} request - WebSocket upgrade request
   * @param {Array} allowedOrigins - Array of allowed origins
   * @returns {boolean} True if origin is allowed
   */
  validateOrigin(request, allowedOrigins = []) {
    const origin = request.headers.origin;
    
    if (!origin) {
      // No origin header (non-browser clients)
      return true;
    }

    if (allowedOrigins.includes('*')) {
      return true;
    }

    if (allowedOrigins.includes(origin)) {
      return true;
    }

    this.logger.warn('Origin not allowed', {
      origin,
      allowedOrigins
    });

    return false;
  }

  /**
   * Creates authentication error response
   * @param {string} code - Error code
   * @param {string} message - Error message
   * @returns {Object} Error response
   */
  createErrorResponse(code, message) {
    return {
      success: false,
      error: message,
      code,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Handles permission updates for active connections
   * @param {string} channel - Redis channel
   * @param {Object} updateData - Permission update data
   * @param {Function} disconnectCallback - Callback to disconnect users
   * @returns {Promise<void>}
   */
  async handlePermissionUpdate(channel, updateData, disconnectCallback) {
    try {
      const { user_id, action, permissions, tenant_id, document_id } = updateData;

      this.logger.info('Processing permission update', {
        userId: user_id,
        tenantId: tenant_id,
        documentId: document_id,
        action,
        permissions
      });

      if (action === 'revoke') {
        // Immediately disconnect user from document
        await disconnectCallback(user_id, document_id, 'permissions_revoked');
        
        this.logger.info('User disconnected due to permission revocation', {
          userId: user_id,
          documentId: document_id
        });
      } else if (action === 'update') {
        // Invalidate cache to force permission refresh
        await this.authService.invalidateCache(user_id, document_id);
        
        this.logger.info('User permissions updated', {
          userId: user_id,
          documentId: document_id,
          newPermissions: permissions
        });
      }
    } catch (error) {
      this.logger.error('Failed to handle permission update', error, {
        channel,
        updateData
      });
    }
  }

  /**
   * Gets middleware statistics
   * @returns {Object} Middleware statistics
   */
  getStats() {
    return {
      authEnabled: this.authService.isEnabled(),
      timestamp: new Date().toISOString()
    };
  }
}

export default AuthMiddleware;
