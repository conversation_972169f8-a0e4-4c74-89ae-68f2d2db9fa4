import React, { useState, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import './TiptapEditor.css';

const CLIENT_ID = Math.floor(Math.random() * 100000);

const CollaborativeEditor = ({ documentId = 'demo-document', serverUrl = 'ws://localhost:1234' }) => {
  const [users, setUsers] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('connecting');
  
  // Create Y.js document and provider
  const [yjsDoc] = useState(() => new Y.Doc());
  const [yjsProvider] = useState(() => new WebsocketProvider(serverUrl, documentId, yjsDoc));

  console.log('🔗 [Client', CLIENT_ID, '] WebSocketProvider:', yjsProvider.url, 'doc:', documentId);

  // Generate random color for user cursor
  function getRandomColor() {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // Set up Y.js provider event listeners
  useEffect(() => {
    console.log('🚀 [Client', CLIENT_ID, '] useEffect - Setting up Y.js collaboration...');
    
    // Set up connection status tracking
    yjsProvider.on('status', (event) => {
      console.log('🌍 [Client', CLIENT_ID, '] WebSocket status:', event.status);
      setConnectionStatus(event.status);
    });

    yjsProvider.on('synced', (isSynced) => {
      console.log('🤝 [Client', CLIENT_ID, '] [YJS SYNCED]', isSynced);
    });

    // Set up awareness for user cursors and count
    yjsProvider.awareness.on('change', () => {
      const states = Array.from(yjsProvider.awareness.getStates().values());
      console.log('👥 [Client', CLIENT_ID, '] Awareness check:', states.length, 'users:', states.map(s => s.user?.name).join(', '), '| wsconnected:', yjsProvider.wsconnected, '| awareness states:', states);
      setUsers(states);
    });

    // Set local user info immediately
    const userName = `User ${Math.floor(Math.random() * 1000)}`;
    const userColor = getRandomColor();
    console.log('👤 [Client', CLIENT_ID, '] Setting local user:', userName, userColor);
    yjsProvider.awareness.setLocalStateField('user', {
      name: userName,
      color: userColor,
    });

    // Force awareness update to ensure user shows up in awareness state immediately
    yjsProvider.awareness.setLocalState(yjsProvider.awareness.getLocalState());

    // Cleanup on unmount
    return () => {
      console.log('🧹 [Client', CLIENT_ID, '] Cleaning up Y.js provider');
      yjsProvider.destroy();
    };
  }, [yjsProvider]);

  // Create collaborative editor with Y.js
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false, // Y.js handles history
      }),
      Underline,
      Collaboration.configure({
        document: yjsDoc,
      }),
      CollaborationCursor.configure({
        provider: yjsProvider,
        user: {
          name: `User ${Math.floor(Math.random() * 1000)}`,
          color: getRandomColor(),
        },
      }),
    ],
    content: '<p>🚀 Start typing to collaborate in real-time!</p>',
    editorProps: {
      attributes: {
        class: 'tiptap-editor-content',
      },
    },
  });

  // Show loading state while editor is initializing
  if (!editor) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Initializing collaborative editor...</p>
        <small>Setting up Tiptap + Y.js collaboration...</small>
        <div style={{ marginTop: '10px', fontSize: '12px', color: '#999' }}>
          Server: {serverUrl} | Document: {documentId}
        </div>
      </div>
    );
  }

  return (
    <div className="tiptap-editor">
      {/* Header with connection status */}
      <div className="editor-header">
        <h2>Tiptap Editor</h2>
        <div className="connection-info">
          <span className={`status ${connectionStatus}`}>
            {connectionStatus === 'connected' ? '🟢' : connectionStatus === 'connecting' ? '🟡' : '🔴'}
            {connectionStatus}
          </span>
          <span className="document-id">Document: {documentId}</span>
          <span className="users-count">👥 {users.length} user(s) connected</span>
          <span className="collaboration-status">✅ Real-time Collaboration Active</span>
        </div>
      </div>

      {/* Toolbar */}
      <div className="toolbar">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'is-active' : ''}
        >
          Bold
        </button>
        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'is-active' : ''}
        >
          Italic
        </button>
        <button
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={editor.isActive('underline') ? 'is-active' : ''}
        >
          Underline
        </button>
        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'is-active' : ''}
        >
          Strike
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
          className={editor.isActive('heading', { level: 1 }) ? 'is-active' : ''}
        >
          H1
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={editor.isActive('heading', { level: 2 }) ? 'is-active' : ''}
        >
          H2
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          className={editor.isActive('heading', { level: 3 }) ? 'is-active' : ''}
        >
          H3
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'is-active' : ''}
        >
          Bullet List
        </button>
        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'is-active' : ''}
        >
          Numbered List
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'is-active' : ''}
        >
          Quote
        </button>
        <button
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'is-active' : ''}
        >
          Code Block
        </button>
      </div>

      {/* Editor */}
      <div className="editor-container">
        <EditorContent editor={editor} />
      </div>

      {/* Footer */}
      <div className="editor-footer">
        <small>
          Real-time collaborative editing powered by Tiptap + Y.js
          {connectionStatus === 'connected' && (
            <span style={{ color: 'green', marginLeft: '10px' }}>
              ✅ Connected to {serverUrl}
            </span>
          )}
        </small>
      </div>
    </div>
  );
};

export default CollaborativeEditor;
