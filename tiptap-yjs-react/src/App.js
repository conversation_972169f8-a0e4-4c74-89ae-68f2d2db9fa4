import React, { useState } from 'react';
import CollaborativeEditor from './components/CollaborativeEditor';
import ErrorBoundary from './components/ErrorBoundary';
import './App.css';
import './components/ErrorBoundary.css';

function App() {
  const [documentId, setDocumentId] = useState('demo-document');
  const [serverUrl] = useState('ws://localhost:1234');

  return (
    <div className="App">
      <div className="app-container">
        <div className="app-header">
          <h1>🚀 Tiptap + Y.js Collaborative Editor</h1>
          <p>Real-time collaborative rich text editing with React, Tiptap, and Y.js</p>

          <div className="document-controls">
            <label htmlFor="documentId">Document ID:</label>
            <input
              id="documentId"
              type="text"
              value={documentId}
              onChange={(e) => setDocumentId(e.target.value)}
              placeholder="Enter document ID"
              className="document-input"
            />
            <span className="server-info">Server: {serverUrl}</span>
          </div>
        </div>

        <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
          <CollaborativeEditor
            documentId={documentId}
            serverUrl={serverUrl}
            key={documentId} // Force re-render when document changes
          />
        </ErrorBoundary>

        <div className="app-footer">
          <div className="instructions">
            <h3>📋 How to test collaboration:</h3>
            <ol>
              <li>Open this page in multiple browser tabs or windows</li>
              <li>Use the same Document ID in all tabs</li>
              <li>Start typing in any tab - changes will sync in real-time!</li>
              <li>Try different formatting options and see live cursors</li>
            </ol>
          </div>

          <div className="features">
            <h3>✨ Features:</h3>
            <ul>
              <li>Real-time collaborative editing</li>
              <li>Live cursor tracking with user awareness</li>
              <li>Rich text formatting (Bold, Italic, Underline, etc.)</li>
              <li>Headings, Lists, Blockquotes, Code blocks</li>
              <li>Conflict-free document synchronization</li>
              <li>WebSocket-based communication</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
