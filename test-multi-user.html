<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-User Collaboration Test</title>
    <script src="https://unpkg.com/yjs@13.6.8/dist/yjs.js"></script>
    <script src="https://unpkg.com/y-websocket@1.5.0/dist/y-websocket.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status-panel {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-item {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }
        .connected { background: #d4edda; color: #155724; }
        .connecting { background: #fff3cd; color: #856404; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .editor {
            border: 1px solid #ddd;
            padding: 15px;
            min-height: 200px;
            border-radius: 8px;
            background: white;
        }
        .user-list {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .user-item {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            margin: 2px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Multi-User Collaboration Test</h1>
    
    <div class="status-panel">
        <h3>Connection Status</h3>
        <div id="connection-status" class="status-item">Initializing...</div>
        <div id="user-count" class="status-item">Users: 0</div>
        <div id="sync-status" class="status-item">Sync: Not synced</div>
        <div id="websocket-state" class="status-item">WebSocket: Unknown</div>
    </div>

    <div class="editor" id="editor" contenteditable="true">
        Start typing here to test real-time collaboration...
    </div>

    <div class="user-list">
        <strong>Connected Users:</strong>
        <div id="user-list"></div>
    </div>

    <div style="margin-top: 20px;">
        <h3>Instructions:</h3>
        <ol>
            <li>Open this page in multiple browser tabs or windows</li>
            <li>Type in the editor to see real-time synchronization</li>
            <li>Check that the user count increases with each new tab</li>
            <li>Verify that connection status shows "connected"</li>
        </ol>
    </div>

    <script>
        // Generate a unique user ID and name
        const userId = 'user-' + Math.random().toString(36).substr(2, 9);
        const userName = 'User ' + Math.floor(Math.random() * 1000);
        
        console.log('🚀 Initializing collaboration test for:', userName, '(ID:', userId, ')');

        // Create Y.js document and WebSocket provider
        const doc = new Y.Doc();
        const provider = new WebsocketProvider('ws://localhost:1234', 'demo-document', doc);
        const yText = doc.getText('content');

        // Get DOM elements
        const editor = document.getElementById('editor');
        const connectionStatus = document.getElementById('connection-status');
        const userCount = document.getElementById('user-count');
        const syncStatus = document.getElementById('sync-status');
        const websocketState = document.getElementById('websocket-state');
        const userList = document.getElementById('user-list');

        // Set up user awareness
        provider.awareness.setLocalStateField('user', {
            name: userName,
            id: userId,
            color: '#' + Math.floor(Math.random()*16777215).toString(16)
        });

        // Update status display
        function updateStatus() {
            const states = Array.from(provider.awareness.getStates().values());
            const usersWithInfo = states.filter(state => state.user && state.user.name);
            
            // Connection status
            if (provider.wsconnected && provider.ws?.readyState === 1) {
                connectionStatus.textContent = '🟢 Connected';
                connectionStatus.className = 'status-item connected';
            } else if (provider.ws?.readyState === 0) {
                connectionStatus.textContent = '🟡 Connecting...';
                connectionStatus.className = 'status-item connecting';
            } else {
                connectionStatus.textContent = '🔴 Disconnected';
                connectionStatus.className = 'status-item disconnected';
            }

            // User count
            userCount.textContent = `Users: ${usersWithInfo.length}`;
            
            // WebSocket state
            const wsState = provider.ws?.readyState;
            const stateNames = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
            websocketState.textContent = `WebSocket: ${stateNames[wsState] || 'UNKNOWN'} (${wsState})`;
            
            // User list
            userList.innerHTML = usersWithInfo.map(state => 
                `<span class="user-item" style="background-color: ${state.user.color}">${state.user.name}</span>`
            ).join('');

            console.log('📊 Status update:', {
                wsconnected: provider.wsconnected,
                readyState: provider.ws?.readyState,
                totalStates: states.length,
                usersWithInfo: usersWithInfo.length,
                users: usersWithInfo.map(s => s.user.name)
            });
        }

        // Event listeners
        provider.on('status', (event) => {
            console.log('🌍 WebSocket status:', event.status);
            updateStatus();
        });

        provider.on('synced', (isSynced) => {
            console.log('🤝 Synced:', isSynced);
            syncStatus.textContent = `Sync: ${isSynced ? 'Synced' : 'Not synced'}`;
            syncStatus.className = `status-item ${isSynced ? 'connected' : 'connecting'}`;
        });

        provider.awareness.on('change', () => {
            console.log('👥 Awareness changed');
            updateStatus();
        });

        provider.on('connection-open', () => {
            console.log('🟢 WebSocket connection opened');
            updateStatus();
        });

        provider.on('connection-close', () => {
            console.log('🔴 WebSocket connection closed');
            updateStatus();
        });

        provider.on('connection-error', (error) => {
            console.log('❌ WebSocket connection error:', error);
            updateStatus();
        });

        // Sync editor content with Y.js
        yText.observe((event) => {
            if (event.transaction.local) return; // Don't update if change was local
            editor.textContent = yText.toString();
        });

        editor.addEventListener('input', () => {
            const content = editor.textContent;
            yText.delete(0, yText.length);
            yText.insert(0, content);
        });

        // Initial status update
        updateStatus();
        
        // Periodic status updates
        setInterval(updateStatus, 2000);

        // Force awareness update
        setTimeout(() => {
            provider.awareness.setLocalState(provider.awareness.getLocalState());
            updateStatus();
        }, 1000);
    </script>
</body>
</html>
