import express from 'express';
import http from 'http';
import { WebSocketServer as WSServer } from 'ws';
import cors from 'cors';
import helmet from 'helmet';
import { setupWSConnection } from '../utils/y-websocket-utils.js';

class WebSocketServer {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = null;
    this.isRunning = false;
    this.yjsService = null;
  }

  initialize() {
    try {
      this.app.use(helmet({
        contentSecurityPolicy: false,
        crossOriginEmbedderPolicy: false
      }));

      this.app.use(cors(this.config.get('cors')));
      this.app.use(express.json({ limit: '10mb' }));
      this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

      this.app.use((req, res, next) => {
        this.logger.http(`${req.method} ${req.url}`, {
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        next();
      });

      this.app.use('/public', express.static('public'));

      this.app.get('/health', (_, res) => {
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          websocket: this.wss ? 'active' : 'inactive'
        });
      });

      this.setupApiRoutes();
      this.setupErrorHandling();
    } catch (error) {
      this.logger.error('Failed to initialize Express server', error);
      throw error;
    }
  }

  setupApiRoutes() {
    const apiRouter = express.Router();

    apiRouter.get('/stats', (_, res) => {
      try {
        if (this.yjsService) {
          const stats = this.yjsService.getStats();
          res.json(stats);
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to get stats', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    this.app.use('/api', apiRouter);
  }

  setupErrorHandling() {
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.url} not found`
      });
    });

    this.app.use((error, req, res, _) => {
      this.logger.error('Unhandled error in Express', error, {
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      });
    });
  }

  initializeWebSocket() {
    try {
      this.wss = new WSServer({ noServer: true });

      this.wss.on('connection', (ws, req) => {
        const url = req.url || '';
        const documentId = url.slice(1).split('?')[0] || 'default';
        const connectionId = `ws-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        const urlParams = new URLSearchParams(url.split('?')[1] || '');
        const userId = urlParams.get('userId') || `user-${Math.random().toString(36).substring(2, 11)}`;

        if (this.yjsService && this.yjsService.connectionManager) {
          try {
            this.yjsService.connectionManager.addConnection(connectionId, ws, {
              documentId,
              userId,
              url: req.url,
              origin: req.headers.origin
            });
          } catch (error) {
            this.logger.error('Failed to add connection to ConnectionManager', error, {
              connectionId,
              documentId
            });
          }
        }

        ws.on('close', () => {
          if (this.yjsService && this.yjsService.connectionManager) {
            try {
              this.yjsService.connectionManager.removeConnection(connectionId);
            } catch (error) {
              this.logger.error('Failed to remove connection from ConnectionManager', error, {
                connectionId
              });
            }
          }
        });

        setupWSConnection(ws, req);
      });

      this.server.on('upgrade', (request, socket, head) => {
        this.wss.handleUpgrade(request, socket, head, (ws) => {
          this.wss.emit('connection', ws, request);
        });
      });

      return this.wss;
    } catch (error) {
      this.logger.error('Failed to initialize WebSocket server', error);
      throw error;
    }
  }

  setYjsService(yjsService) {
    this.yjsService = yjsService;
  }

  async start() {
    try {
      const port = this.config.get('port');
      const host = this.config.get('host');

      await new Promise((resolve, reject) => {
        this.server.listen(port, host, (error) => {
          if (error) {
            reject(error);
          } else {
            this.isRunning = true;
            this.logger.info(`Server started on ${host}:${port}`);
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.error('Failed to start server', error);
      throw error;
    }
  }

  async stop() {
    try {
      if (!this.isRunning) {
        return;
      }

      if (this.wss) {
        this.wss.close();
      }

      await new Promise((resolve) => {
        this.server.close(() => {
          this.isRunning = false;
          resolve();
        });
      });
    } catch (error) {
      this.logger.error('Failed to stop server', error);
      throw error;
    }
  }

  getWebSocketServer() {
    return this.wss;
  }

  isServerRunning() {
    return this.isRunning;
  }
}

export default WebSocketServer;
