import Redis from 'ioredis';
import IPermissionCache from '../interfaces/IPermissionCache.js';

/**
 * Redis-based permission cache implementation
 * Follows Single Responsibility Principle (SRP)
 */
class RedisPermissionCache extends IPermissionCache {
  constructor(config, logger) {
    super();
    this.config = config;
    this.logger = logger;
    this.cacheConfig = config.get('auth').cache;
    this.redisConfig = config.get('redis');
    
    this.client = null;
    this.subscriber = null;
    this.isConnected = false;
  }

  /**
   * Initialize Redis connections
   * @returns {Promise<void>}
   */
  async initialize() {
    if (!this.isEnabled()) {
      this.logger.info('Permission cache is disabled');
      return;
    }

    try {
      // Main Redis client for cache operations
      this.client = new Redis({
        ...this.parseRedisUrl(this.redisConfig.url),
        db: this.redisConfig.authDb,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      // Separate client for pub/sub
      this.subscriber = new Redis({
        ...this.parseRedisUrl(this.redisConfig.url),
        db: this.redisConfig.authDb,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      // Connect to Redis
      await this.client.connect();
      await this.subscriber.connect();

      this.isConnected = true;
      this.logger.info('Redis permission cache initialized successfully');

      // Set up error handlers
      this.client.on('error', (error) => {
        this.logger.error('Redis client error', error);
        this.isConnected = false;
      });

      this.subscriber.on('error', (error) => {
        this.logger.error('Redis subscriber error', error);
      });

    } catch (error) {
      this.logger.error('Failed to initialize Redis permission cache', error);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Parse Redis URL into connection options
   * @param {string} url - Redis URL
   * @returns {Object} Connection options
   */
  parseRedisUrl(url) {
    try {
      const parsed = new URL(url);
      return {
        host: parsed.hostname,
        port: parseInt(parsed.port) || 6379,
        password: parsed.password || undefined,
        username: parsed.username || undefined
      };
    } catch (error) {
      this.logger.warn('Failed to parse Redis URL, using defaults', { url, error: error.message });
      return {
        host: 'localhost',
        port: 6379
      };
    }
  }

  /**
   * Gets cached permissions for a user and document
   * @param {string} tenantId - Tenant ID
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @returns {Promise<Object|null>} Cached permissions or null
   */
  async get(tenantId, userId, documentId) {
    if (!this.isEnabled() || !this.isConnected) {
      return null;
    }

    try {
      const key = this.generateCacheKey(tenantId, userId, documentId);
      const cached = await this.client.get(key);
      
      if (cached) {
        const permissions = JSON.parse(cached);
        this.logger.debug('Cache hit for permissions', {
          tenantId,
          userId,
          documentId,
          permissions: permissions.permissions
        });
        return permissions;
      }

      this.logger.debug('Cache miss for permissions', {
        tenantId,
        userId,
        documentId
      });
      return null;
    } catch (error) {
      this.logger.error('Failed to get cached permissions', error, {
        tenantId,
        userId,
        documentId
      });
      return null;
    }
  }

  /**
   * Sets cached permissions for a user and document
   * @param {string} tenantId - Tenant ID
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @param {Object} permissions - Permission object to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<void>}
   */
  async set(tenantId, userId, documentId, permissions, ttl = null) {
    if (!this.isEnabled() || !this.isConnected) {
      return;
    }

    try {
      const key = this.generateCacheKey(tenantId, userId, documentId);
      const value = JSON.stringify({
        ...permissions,
        cachedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (ttl || this.cacheConfig.ttl) * 1000).toISOString()
      });

      await this.client.setex(key, ttl || this.cacheConfig.ttl, value);
      
      this.logger.debug('Cached permissions', {
        tenantId,
        userId,
        documentId,
        ttl: ttl || this.cacheConfig.ttl
      });
    } catch (error) {
      this.logger.error('Failed to cache permissions', error, {
        tenantId,
        userId,
        documentId
      });
    }
  }

  /**
   * Deletes cached permissions
   * @param {string} tenantId - Tenant ID
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID (optional)
   * @returns {Promise<void>}
   */
  async delete(tenantId, userId, documentId = null) {
    if (!this.isEnabled() || !this.isConnected) {
      return;
    }

    try {
      if (documentId) {
        // Delete specific document permissions
        const key = this.generateCacheKey(tenantId, userId, documentId);
        await this.client.del(key);
      } else {
        // Delete all permissions for user in tenant
        const pattern = this.generateCacheKey(tenantId, userId, '*');
        const keys = await this.client.keys(pattern);
        if (keys.length > 0) {
          await this.client.del(...keys);
        }
      }

      this.logger.debug('Deleted cached permissions', {
        tenantId,
        userId,
        documentId
      });
    } catch (error) {
      this.logger.error('Failed to delete cached permissions', error, {
        tenantId,
        userId,
        documentId
      });
    }
  }

  /**
   * Subscribes to permission update events
   * @param {Function} callback - Callback function for permission updates
   * @returns {Promise<void>}
   */
  async subscribeToUpdates(callback) {
    if (!this.isEnabled() || !this.isConnected) {
      return;
    }

    try {
      const pattern = 'permissions:*';
      await this.subscriber.psubscribe(pattern);
      
      this.subscriber.on('pmessage', (pattern, channel, message) => {
        try {
          const updateData = JSON.parse(message);
          this.logger.debug('Received permission update', {
            channel,
            updateData
          });
          callback(channel, updateData);
        } catch (error) {
          this.logger.error('Failed to process permission update message', error, {
            channel,
            message
          });
        }
      });

      this.logger.info('Subscribed to permission updates');
    } catch (error) {
      this.logger.error('Failed to subscribe to permission updates', error);
    }
  }

  /**
   * Publishes permission update event
   * @param {string} tenantId - Tenant ID
   * @param {string} documentId - Document ID
   * @param {Object} updateData - Update data
   * @returns {Promise<void>}
   */
  async publishUpdate(tenantId, documentId, updateData) {
    if (!this.isEnabled() || !this.isConnected) {
      return;
    }

    try {
      const channel = `permissions:${tenantId}:${documentId}`;
      const message = JSON.stringify({
        ...updateData,
        timestamp: new Date().toISOString(),
        tenantId,
        documentId
      });

      await this.client.publish(channel, message);
      
      this.logger.debug('Published permission update', {
        channel,
        updateData
      });
    } catch (error) {
      this.logger.error('Failed to publish permission update', error, {
        tenantId,
        documentId,
        updateData
      });
    }
  }

  /**
   * Checks if caching is enabled
   * @returns {boolean} True if caching is enabled
   */
  isEnabled() {
    return this.cacheConfig.enabled;
  }

  /**
   * Generate cache key for permissions
   * @param {string} tenantId - Tenant ID
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @returns {string} Cache key
   */
  generateCacheKey(tenantId, userId, documentId) {
    return `auth:${tenantId}:${userId}:${documentId}`;
  }

  /**
   * Close Redis connections
   * @returns {Promise<void>}
   */
  async close() {
    try {
      if (this.client) {
        await this.client.quit();
      }
      if (this.subscriber) {
        await this.subscriber.quit();
      }
      this.isConnected = false;
      this.logger.info('Redis permission cache connections closed');
    } catch (error) {
      this.logger.error('Failed to close Redis connections', error);
    }
  }

  /**
   * Get cache statistics
   * @returns {Promise<Object>} Cache statistics
   */
  async getStats() {
    if (!this.isEnabled() || !this.isConnected) {
      return { enabled: false, connected: false };
    }

    try {
      const info = await this.client.info('memory');
      const keyCount = await this.client.dbsize();

      return {
        enabled: true,
        connected: this.isConnected,
        keyCount,
        memoryInfo: this.parseRedisInfo(info)
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats', error);
      return { enabled: true, connected: false, error: error.message };
    }
  }

  /**
   * Parse Redis INFO response
   * @param {string} info - Redis INFO response
   * @returns {Object} Parsed memory info
   */
  parseRedisInfo(info) {
    const lines = info.split('\r\n');
    const result = {};

    lines.forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (key.includes('memory')) {
          result[key] = value;
        }
      }
    });

    return result;
  }
}

export default RedisPermissionCache;
