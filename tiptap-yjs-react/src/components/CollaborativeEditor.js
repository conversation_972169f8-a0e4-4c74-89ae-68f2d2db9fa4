import React, { useState, useEffect, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import './TiptapEditor.css';

// Global storage for Y.js providers to prevent recreation
const globalProviders = new Map();

const getOrCreateProvider = (serverUrl, documentId) => {
  const key = `${serverUrl}/${documentId}`;

  if (!globalProviders.has(key)) {
    const doc = new Y.Doc();
    const provider = new WebsocketProvider(serverUrl, documentId, doc);

    const entry = {
      doc,
      provider,
      refCount: 0,
      currentUsers: [],
      connectionStatus: 'connecting'
    };
    globalProviders.set(key, entry);

    // Clean up on page unload
    if (!window.yjsCleanupRegistered) {
      window.yjsCleanupRegistered = true;
      window.addEventListener('beforeunload', () => {
        globalProviders.forEach((entry) => {
          entry.provider.destroy();
        });
        globalProviders.clear();
      });
    }
  }

  const entry = globalProviders.get(key);
  entry.refCount++;
  return entry;
};

const setupProviderListeners = (entry) => {
  if (entry.listenersSetup) return;

  const { provider } = entry;

  // Update awareness and connection status
  const updateState = () => {
    const states = Array.from(provider.awareness.getStates().values());
    const usersWithInfo = states.filter(state => state.user && state.user.name);
    entry.currentUsers = usersWithInfo;

    if (provider.wsconnected && provider.ws?.readyState === 1) {
      entry.connectionStatus = 'connected';
    } else if (provider.ws?.readyState === 0) {
      entry.connectionStatus = 'connecting';
    } else {
      entry.connectionStatus = 'disconnected';
    }
  };

  // Set up listeners
  provider.on('status', updateState);
  provider.on('synced', updateState);
  provider.awareness.on('change', updateState);

  // Periodic status check
  entry.statusInterval = setInterval(updateState, 1000);
  entry.listenersSetup = true;
};

const releaseProvider = (serverUrl, documentId) => {
  const key = `${serverUrl}/${documentId}`;
  const entry = globalProviders.get(key);

  if (entry) {
    entry.refCount--;

    // In development mode, keep providers alive to avoid React StrictMode issues
    if (process.env.NODE_ENV === 'development') {
      return;
    }

    // In production, destroy after delay if no references
    if (entry.refCount <= 0) {
      if (entry.destroyTimeout) {
        clearTimeout(entry.destroyTimeout);
      }

      entry.destroyTimeout = setTimeout(() => {
        if (entry.refCount <= 0) {
          if (entry.statusInterval) {
            clearInterval(entry.statusInterval);
          }
          entry.provider.destroy();
          globalProviders.delete(key);
        }
      }, 5000);
    }
  }
};

const CollaborativeEditor = ({ documentId = 'demo-document', serverUrl = 'ws://localhost:1234' }) => {
  const [users, setUsers] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('connecting');
  const [isInitialized, setIsInitialized] = useState(false);

  // Get or create the provider
  const providerEntry = useRef(null);
  const userInfoRef = useRef(null);
  const syncIntervalRef = useRef(null);

  if (!providerEntry.current) {
    providerEntry.current = getOrCreateProvider(serverUrl, documentId);
  }

  const { doc: yjsDoc, provider: yjsProvider } = providerEntry.current;

  // Generate random color for user cursor
  function getRandomColor() {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // Generate user info only once
  if (!userInfoRef.current) {
    userInfoRef.current = {
      name: `User ${Math.floor(Math.random() * 1000)}`,
      color: getRandomColor()
    };
  }

  // Initialize Y.js collaboration
  useEffect(() => {
    if (isInitialized) return;

    setupProviderListeners(providerEntry.current);
    yjsProvider.awareness.setLocalStateField('user', userInfoRef.current);
    setIsInitialized(true);

    return () => {
      releaseProvider(serverUrl, documentId);
      providerEntry.current = null;
    };
  }, [yjsProvider, isInitialized, serverUrl, documentId]);

  // Sync component state with global provider state
  useEffect(() => {
    if (!syncIntervalRef.current) {
      syncIntervalRef.current = setInterval(() => {
        const entry = providerEntry.current;
        if (entry) {
          setUsers(entry.currentUsers);
          setConnectionStatus(entry.connectionStatus);
        }
      }, 500);
    }

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
    };
  }, []);

  // Create collaborative editor with Y.js
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false, // Y.js handles history
      }),
      Underline,
      Collaboration.configure({
        document: yjsDoc,
      }),
      CollaborationCursor.configure({
        provider: yjsProvider,
        user: userInfoRef.current,
      }),
    ],
    content: '<p>🚀 Start typing to collaborate in real-time!</p>',
    editorProps: {
      attributes: {
        class: 'tiptap-editor-content',
      },
    },
  }, [yjsDoc, yjsProvider]); // Add dependencies to ensure editor updates when provider changes

  // Show loading state while editor is initializing
  if (!editor) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Initializing collaborative editor...</p>
        <small>Setting up Tiptap + Y.js collaboration...</small>
        <div style={{ marginTop: '10px', fontSize: '12px', color: '#999' }}>
          Server: {serverUrl} | Document: {documentId}
        </div>
      </div>
    );
  }

  return (
    <div className="tiptap-editor">
      {/* Header with connection status */}
      <div className="editor-header">
        <h2>Tiptap Editor</h2>
        <div className="connection-info">
          <span className={`status ${connectionStatus}`}>
            {connectionStatus === 'connected' ? '🟢' : connectionStatus === 'connecting' ? '🟡' : '🔴'}
            {connectionStatus}
          </span>
          <span className="document-id">Document: {documentId}</span>
          <span className="users-count">👥 {users.length} user(s) connected</span>
          <span className="collaboration-status">✅ Real-time Collaboration Active</span>
        </div>
      </div>

      {/* Toolbar */}
      <div className="toolbar">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'is-active' : ''}
        >
          Bold
        </button>
        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'is-active' : ''}
        >
          Italic
        </button>
        <button
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={editor.isActive('underline') ? 'is-active' : ''}
        >
          Underline
        </button>
        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'is-active' : ''}
        >
          Strike
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
          className={editor.isActive('heading', { level: 1 }) ? 'is-active' : ''}
        >
          H1
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={editor.isActive('heading', { level: 2 }) ? 'is-active' : ''}
        >
          H2
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          className={editor.isActive('heading', { level: 3 }) ? 'is-active' : ''}
        >
          H3
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'is-active' : ''}
        >
          Bullet List
        </button>
        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'is-active' : ''}
        >
          Numbered List
        </button>
        <div className="separator"></div>
        <button
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'is-active' : ''}
        >
          Quote
        </button>
        <button
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'is-active' : ''}
        >
          Code Block
        </button>
      </div>

      {/* Editor */}
      <div className="editor-container">
        <EditorContent editor={editor} />
      </div>

      {/* Footer */}
      <div className="editor-footer">
        <small>
          Real-time collaborative editing powered by Tiptap + Y.js
          {connectionStatus === 'connected' && (
            <span style={{ color: 'green', marginLeft: '10px' }}>
              ✅ Connected to {serverUrl}
            </span>
          )}
        </small>
      </div>
    </div>
  );
};

export default CollaborativeEditor;
