# 🚀 Real-time Y.js WebSocket Server - Makefile
# Easy commands for development, testing, and deployment

# Variables - can be overridden by environment variables
DOCKER_IMAGE_NAME ?= realtime-yjs-server
DOCKER_TAG ?= latest
CONTAINER_NAME ?= yjs-server
PORT ?= 1234
FRONTEND_PORT ?= 3000

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

.PHONY: help install dev build start stop restart logs clean test docker-build docker-run docker-stop docker-clean docker-status docker-test production scale-up scale-down generate-haproxy-config

# Default target
help: ## Show this help message
	@echo "$(BLUE)🚀 Real-time Y.js WebSocket Server$(NC)"
	@echo "$(YELLOW)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development Commands
install: ## Install dependencies
	@echo "$(YELLOW)📦 Installing dependencies...$(NC)"
	npm install
	@echo "$(GREEN)✅ Dependencies installed$(NC)"

dev: ## Start development server with auto-reload
	@echo "$(YELLOW)🔧 Starting development server...$(NC)"
	npm run dev

start: ## Start production server
	@echo "$(YELLOW)🚀 Starting production server...$(NC)"
	npm start

stop: ## Stop the server (if running with PM2)
	@echo "$(YELLOW)🛑 Stopping server...$(NC)"
	-pkill -f "node src/index.js"
	@echo "$(GREEN)✅ Server stopped$(NC)"

restart: stop start ## Restart the server

logs: ## Show server logs
	@echo "$(YELLOW)📋 Showing logs...$(NC)"
	tail -f logs/app.log 2>/dev/null || echo "$(RED)❌ No log file found$(NC)"

test: ## Run tests
	@echo "$(YELLOW)🧪 Running tests...$(NC)"
	npm test

clean: ## Clean node_modules and logs
	@echo "$(YELLOW)🧹 Cleaning up...$(NC)"
	rm -rf node_modules
	rm -rf logs/*.log
	rm -rf package-lock.json
	@echo "$(GREEN)✅ Cleanup complete$(NC)"

# Docker Commands (Scalable Setup)
docker-build: ## Build Docker images for scalable setup
	@echo "$(YELLOW)🐳 Building Docker images for scalable setup...$(NC)"
	@docker-compose -f docker-compose.scalable.yml build
	@echo "$(GREEN)✅ Docker images built for scalable setup$(NC)"



docker-run: docker-build ## Build and run scalable setup
	@echo "$(YELLOW)🚀 Starting scalable Docker setup...$(NC)"
	@docker-compose -f docker-compose.scalable.yml up -d
	@echo "$(GREEN)✅ Scalable setup started!$(NC)"
	@echo "$(CYAN)📊 Access points:$(NC)"
	@echo "  • Main app: http://localhost:$(PORT)"
	@echo "  • Load balancer stats: http://localhost:8080/stats"
	@echo "  • Redis: localhost:6379"



docker-stop: ## Stop scalable Docker setup
	@echo "$(YELLOW)🛑 Stopping scalable Docker setup...$(NC)"
	@docker-compose -f docker-compose.scalable.yml down
	@echo "$(GREEN)✅ Scalable setup stopped$(NC)"

docker-logs: ## Show logs from scalable setup
	@echo "$(YELLOW)📋 Scalable setup logs:$(NC)"
	@docker-compose -f docker-compose.scalable.yml logs --tail=50 -f

docker-shell: ## Open shell in running Y.js server container
	@echo "$(YELLOW)🐚 Opening shell in Y.js server container...$(NC)"
	@docker exec -it yjs-server-1 /bin/sh

docker-clean: docker-stop ## Clean Docker images and containers
	@echo "$(YELLOW)🧹 Cleaning Docker resources...$(NC)"
	-docker rmi $(DOCKER_IMAGE_NAME):$(DOCKER_TAG)
	@docker system prune -f
	@echo "$(GREEN)✅ Docker cleanup complete$(NC)"

# 🚀 Additional Scalable Commands
docker-status: ## Show status of scalable setup
	@echo "$(YELLOW)📊 Scalable setup status:$(NC)"
	@docker-compose -f docker-compose.scalable.yml ps
	@echo ""
	@echo "$(CYAN)🔍 Testing load balancer:$(NC)"
	@curl -s http://localhost:1234/health | jq . || echo "$(RED)❌ Load balancer not responding$(NC)"

docker-test: ## Test load balancing with multiple requests
	@echo "$(YELLOW)🧪 Testing load balancing...$(NC)"
	@for i in {1..5}; do \
		echo "Request $$i:"; \
		curl -s http://localhost:1234/health | jq -r '.timestamp' || echo "Failed"; \
		sleep 1; \
	done
	@echo "$(GREEN)✅ Load balancing test complete$(NC)"

compose-logs: ## Show docker-compose logs
	@echo "$(YELLOW)📋 Showing docker-compose logs...$(NC)"
	docker-compose logs -f

compose-build: ## Build docker-compose services
	@echo "$(YELLOW)🔨 Building docker-compose services...$(NC)"
	docker-compose build
	@echo "$(GREEN)✅ Services built$(NC)"

# Production Commands
production: ## Deploy to production with nginx
	@echo "$(YELLOW)🚀 Deploying to production...$(NC)"
	docker-compose --profile production up -d
	@echo "$(GREEN)✅ Production deployment complete$(NC)"

# Health Check Commands
health: ## Check server health
	@echo "$(YELLOW)🏥 Checking server health...$(NC)"
	@curl -s http://localhost:$(PORT)/health | jq . || echo "$(RED)❌ Health check failed$(NC)"

stats: ## Show server statistics
	@echo "$(YELLOW)📊 Server statistics:$(NC)"
	@curl -s http://localhost:$(PORT)/api/stats | jq . || echo "$(RED)❌ Stats unavailable$(NC)"

# Frontend Commands
frontend-install: ## Install frontend dependencies
	@echo "$(YELLOW)📦 Installing frontend dependencies...$(NC)"
	cd tiptap-yjs-react && npm install
	@echo "$(GREEN)✅ Frontend dependencies installed$(NC)"

frontend-start: ## Start frontend development server
	@echo "$(YELLOW)🎨 Starting frontend server...$(NC)"
	cd tiptap-yjs-react && npm start

frontend-build: ## Build frontend for production
	@echo "$(YELLOW)🔨 Building frontend...$(NC)"
	cd tiptap-yjs-react && npm run build
	@echo "$(GREEN)✅ Frontend built$(NC)"

# Full Stack Commands
full-install: install frontend-install ## Install all dependencies (backend + frontend)

full-dev: ## Start both backend and frontend in development
	@echo "$(YELLOW)🚀 Starting full development environment...$(NC)"
	@echo "$(BLUE)Starting backend server...$(NC)"
	npm run dev &
	@echo "$(BLUE)Waiting for backend to start...$(NC)"
	sleep 3
	@echo "$(BLUE)Starting frontend server...$(NC)"
	cd tiptap-yjs-react && npm start

full-stop: ## Stop all development servers
	@echo "$(YELLOW)🛑 Stopping all servers...$(NC)"
	-pkill -f "node src/index.js"
	-pkill -f "react-scripts start"
	@echo "$(GREEN)✅ All servers stopped$(NC)"

# Utility Commands
check-ports: ## Check if ports are available
	@echo "$(YELLOW)🔍 Checking ports...$(NC)"
	@echo "Backend port $(PORT):"
	@lsof -i :$(PORT) || echo "  $(GREEN)✅ Port $(PORT) is available$(NC)"
	@echo "Frontend port $(FRONTEND_PORT):"
	@lsof -i :$(FRONTEND_PORT) || echo "  $(GREEN)✅ Port $(FRONTEND_PORT) is available$(NC)"

setup: install frontend-install docker-build ## Complete project setup
	@echo "$(GREEN)🎉 Project setup complete!$(NC)"
	@echo "$(YELLOW)Next steps:$(NC)"
	@echo "  $(BLUE)make dev$(NC)          - Start development server"
	@echo "  $(BLUE)make frontend-start$(NC) - Start frontend (in another terminal)"
	@echo "  $(BLUE)make docker-run$(NC)     - Run with Docker"
	@echo "  $(BLUE)make full-dev$(NC)       - Start both backend and frontend"

# Quick start commands
quick-start: setup full-dev ## Complete setup and start development environment

# Monitoring Commands
monitor: ## Monitor server in real-time
	@echo "$(YELLOW)📊 Monitoring server...$(NC)"
	@while true; do \
		clear; \
		echo "$(BLUE)=== Server Health ===$(NC)"; \
		curl -s http://localhost:$(PORT)/health 2>/dev/null | jq . || echo "$(RED)Server not responding$(NC)"; \
		echo ""; \
		echo "$(BLUE)=== Server Stats ===$(NC)"; \
		curl -s http://localhost:$(PORT)/api/stats 2>/dev/null | jq . || echo "$(RED)Stats not available$(NC)"; \
		echo ""; \
		echo "$(YELLOW)Press Ctrl+C to stop monitoring$(NC)"; \
		sleep 5; \
	done

# 🚀 Scalable Production Commands
generate-haproxy-config: ## Generate HAProxy configuration from environment variables
	@echo "$(YELLOW)🔧 Generating HAProxy configuration...$(NC)"
	./scripts/generate-haproxy-config.sh

scale-up: generate-haproxy-config ## Scale up the application (2 Y.js servers + HAProxy + Redis)
	@echo "$(YELLOW)🚀 Scaling up application...$(NC)"
	docker-compose -f docker-compose.scalable.yml up -d
	@echo "$(GREEN)✅ Application scaled up successfully$(NC)"
	@echo "$(BLUE)🌐 Access your application at: http://localhost:1234$(NC)"
	@echo "$(BLUE)📊 HAProxy stats at: http://localhost:8080/stats$(NC)"

scale-down: ## Scale down the application
	@echo "$(YELLOW)⬇️ Scaling down application...$(NC)"
	docker-compose -f docker-compose.scalable.yml down
	@echo "$(GREEN)✅ Application scaled down$(NC)"

docker-status: ## Show status of scalable setup
	@echo "$(YELLOW)📊 Scalable setup status:$(NC)"
	@docker-compose -f docker-compose.scalable.yml ps
	@echo ""
	@echo "$(CYAN)🔍 Testing load balancer:$(NC)"
	@curl -s http://localhost/health | jq . || echo "$(RED)Load balancer not responding$(NC)"
